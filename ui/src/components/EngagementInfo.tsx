import { Link, useLocation } from "@tanstack/react-router";
import { format } from "date-fns";
import { MdOut<PERSON><PERSON>ock<PERSON>erson, MdOutlinePersonSearch } from "react-icons/md";

import { Engagement, EngagementUser } from "../model";
import CreateAccountsButton from "./AddAccounts/CreateAccountsButton";
import CreateNodeButton from "./CreateNode/CreateNodeButton";
import EditEngagement from "./EditEngagement/EditEngagement";

type Props = {
  engagement: Engagement;
};

export default function EngagementInfo({ engagement }: Props) {
  const current = useLocation();

  return (
    <>
      <div className="mt-2 flex flex-col space-y-4 md:grid md:grid-cols-[1fr_auto] md:items-start md:gap-4 md:space-y-0">
        <div className="flex flex-col space-y-2">
          <div>
            <span className="text-base font-semibold text-slate-400 dark:text-white">
              Client:{" "}
            </span>
            <span className="text-base font-semibold text-slate-700 dark:text-slate-400">
              {engagement.client_name}
            </span>
          </div>
          <span className="text-sm font-normal text-gray-400 dark:text-white">
            Last update: {format(new Date(), "MMMM dd, yyyy | HH:mm aa")}
          </span>
        </div>
        {!current.pathname.includes("logs") && (
          <div className="flex min-h-[44px] flex-row flex-wrap items-center gap-3 md:justify-end md:col-start-2 md:col-end-3 max-w-full overflow-x-auto md:overflow-visible md:ml-0">
            <EditEngagement
              engagementID={engagement.id}
              engagement={engagement}
            />

            <CreateNodeButton engagementID={engagement.id} />

            <CreateAccountsButton engagementID={engagement.id} />
          </div>
        )}
        <div className="md:hidden">
          {current.pathname.includes("logs") ? (
            <Link
              to={`/engagements/$engagementId`}
              params={{ engagementId: engagement.id }}
              className="inline-block text-indigo-600 underline underline-offset-2 hover:text-indigo-400 dark:text-indigo-300 dark:hover:text-indigo-200"
            >
              Engagement Details
            </Link>
          ) : (
            <Link
              to={`/engagements/$engagementId/user-assignment-logs`}
              params={{ engagementId: engagement.id }}
              className="inline-block text-indigo-600 underline underline-offset-2 hover:text-indigo-400 dark:text-indigo-300 dark:hover:text-indigo-200"
            >
              User Assignment Logs
            </Link>
          )}
        </div>
      </div>

      <div className="mt-6 flex flex-col justify-start space-y-4 md:flex-row md:space-y-0 md:space-x-10">
        <div className="flex flex-col space-y-2">
          <div className="text-sm font-bold text-black dark:text-white">
            WBS Code
          </div>
          <div className="w-60 rounded-sm border border-solid border-gray-300 px-2 py-3 text-base font-normal text-gray-400 dark:text-white">
            {engagement?.wbs_code || "-"}
          </div>
        </div>
        <div className="mt-4 md:mt-0">
          <div className="text-sm font-bold text-black dark:text-white">
            Users
          </div>
          <div className="flex flex-row space-x-2 pt-2 md:pt-4">
            {engagement?.users && engagement.users.length > 0 ? (
              engagement.users.map((user: EngagementUser) => (
                <div
                  key={user.username}
                  className="flex items-center rounded-full border border-solid border-gray-400 bg-white px-5 py-1 dark:bg-purple-950 dark:text-white"
                >
                  <div className="has-tooltip">
                    <span className="tooltip -mt-8 rounded-sm bg-gray-100 p-1 text-red-500 shadow-lg">
                      Missing Username
                    </span>
                    {!user.valid_custom_username && (
                      <MdOutlinePersonSearch className="h-5 w-5 text-rose-600" />
                    )}
                  </div>
                  <div className="has-tooltip">
                    <span className="tooltip -mt-8 rounded-sm bg-gray-100 p-1 text-red-500 shadow-lg">
                      Missing SSH Key
                    </span>
                    {!user.valid_ssh_key && (
                      <MdOutlineLockPerson className="h-5 w-5 text-rose-600" />
                    )}
                  </div>
                  <span
                    className={
                      !user.valid_ssh_key || !user.valid_custom_username
                        ? "ml-2"
                        : ""
                    }
                  >
                    {user.full_name}
                  </span>
                </div>
              ))
            ) : (
              <div className="dark:text-white">-</div>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
