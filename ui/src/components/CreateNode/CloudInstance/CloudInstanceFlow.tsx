import { useMsal } from "@azure/msal-react";
import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
  Listbox,
  ListboxButton,
  ListboxOption,
  ListboxOptions,
} from "@headlessui/react";
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { useEffect, useMemo, useState } from "react";
import { HiChevronDown, HiChevronLeft } from "react-icons/hi";
import { HiXMark } from "react-icons/hi2";
import { RiAdminLine, RiUserLine } from "react-icons/ri";
import { toast } from "react-toastify";

import {
  PostNodesCloudInstanceMutationBody,
  getAdminScripts,
  getGetAdminScriptsQueryKey,
  getGetAzureInstanceTypesQueryKey,
  getGetEngagementGraphsQueryKey,
  getGetInstanceTypesQueryKey,
  getGetProvidersAwsAmisQueryKey,
  getGetProvidersAwsRegionsQueryKey,
  getGetProvidersAzureAmisQueryKey,
  getGetProvidersAzureRegionsQueryKey,
  getGetUserScriptsQueryKey,
  getUserScripts,
  postNodesCloudInstance,
  useGetAzureInstanceTypes,
  useGetEngagementCloudAccounts,
  useGetInstanceTypes,
  useGetProvidersAwsAmis,
  useGetProvidersAwsRegions,
  useGetProvidersAzureAmis,
  useGetProvidersAzureRegions,
} from "../../../client.ts";
import { useRateLimitCooldown } from "../../../hooks/useRateLimitCooldown";
import { Script } from "../../../model";
import { CloudAccount } from "../../../model";
import { validationSchemaType } from "../../../utils/validationutils.ts";
import { getZodErrorMessage } from "../../../utils/zodHelpers";
import { ActionButtons, ButtonProps } from "../../ActionButtons.tsx";
import ErrorMessageModal, { ErrorMessage } from "../../ErrorMessageModal.tsx";
import HighlightedTextarea from "../../HighlightedTextArea.tsx";
import { CreateNodeScreen, ModalProps } from "../types.ts";
import { AmiAws, AmiAzure, CloudInstanceType } from "./types.ts";

type NormalizedAMI = {
  name: string;
  description: string;
  image_id: string; // e.g., AMI ID (AWS) or version (Azure)
};

type AMI = AmiAws | AmiAzure;

export default function CloudInstanceFlow({
  engagementID,
  nodeGroupID,
  setCreateNodeScreen,
  closeModal,
  setNodeType,
}: ModalProps) {
  const queryClient = useQueryClient();
  const [isOpenErrorModal, setIsOpenErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);
  const { cooldown, triggerCooldown, isBlocked } = useRateLimitCooldown(
    "cloud_instance_create_cooldown",
  );

  const { data: cloudAccountsData } =
    useGetEngagementCloudAccounts(engagementID);

  const [selectedAccount, setSelectedAccount] = useState<CloudAccount | null>(
    null,
  );

  // Region
  const [selectedRegion, setSelectedRegion] = useState<string>("");
  const [selectedOperatingSystem, setSelectedOperatingSystem] =
    useState<AMI | null>(null);
  const [ports, setSelectedPorts] = useState<number[]>([22]);
  const [regionQuery, setRegionQuery] = useState("");
  const [sizeQuery, setSizeQuery] = useState("");

  const isAWS = selectedAccount?.provider === "AWS";
  const isAzure = selectedAccount?.provider === "AZURE";

  const { data: regionsList } = useGetProvidersAwsRegions({
    query: {
      queryKey: getGetProvidersAwsRegionsQueryKey(),
      enabled: isAWS,
    },
  });

  const regions =
    regionsList?.prioritizedRegions && regionsList.prioritizedRegions.length > 0
      ? regionsList.prioritizedRegions
      : regionsList?.regions || [];

  const { data: azureRegionsList } = useGetProvidersAzureRegions(
    { tenantId: selectedAccount?.display_name },
    {
      query: {
        queryKey: getGetProvidersAzureRegionsQueryKey(),
        enabled: isAzure && !!selectedAccount?.display_name,
      },
    },
  );

  const { data: awsInstanceTypesList, isLoading: isLoadingAwsInstanceTypes } =
    useGetInstanceTypes(
      selectedRegion,
      selectedOperatingSystem?.image_id || "",
      {
        query: {
          queryKey: getGetInstanceTypesQueryKey(
            selectedRegion,
            selectedOperatingSystem?.image_id || "",
          ),
          enabled:
            isAWS && !!selectedRegion && !!selectedOperatingSystem?.image_id,
        },
      },
    );

  const {
    data: azureInstanceTypesList,
    isLoading: isLoadingAzureInstanceTypes,
  } = useGetAzureInstanceTypes(selectedRegion, {
    query: {
      queryKey: getGetAzureInstanceTypesQueryKey(selectedRegion),
      enabled: isAzure && !!selectedRegion,
    },
  });

  const instance_types = isAWS
    ? awsInstanceTypesList?.instance_types || []
    : isAzure
      ? azureInstanceTypesList?.instance_types || []
      : [];

  const isLoadingInstanceTypes = isAWS
    ? isLoadingAwsInstanceTypes
    : isAzure
      ? isLoadingAzureInstanceTypes
      : false;

  const filteredInstanceTypes = instance_types.filter(
    (instanceType) =>
      !sizeQuery ||
      instanceType.alias?.toLowerCase().includes(sizeQuery.toLowerCase()) ||
      instanceType.type?.toLowerCase().includes(sizeQuery.toLowerCase()),
  );

  const { data: operatingSystemsList } = useGetProvidersAwsAmis(
    selectedRegion,
    {
      query: {
        queryKey: [
          getGetProvidersAwsAmisQueryKey(selectedRegion),
          selectedAccount?.provider,
        ],
        enabled: isAWS && !!selectedRegion,
      },
    },
  );

  // Name
  const [name, setName] = useState("");
  const [error, setError] = useState("");

  // Size/Instance Type
  const [selectedCloudInstanceType, setSelectedCloudInstanceType] =
    useState<CloudInstanceType | null>(null);
  const [isSizeValid, setIsSizeValid] = useState<boolean>(false);
  const [instanceTypeUrl, setInstanceTypeUrl] = useState<string>("");

  // Disable instance type selection until AMI is selected
  const isInstanceTypeSelectionEnabled = !!selectedOperatingSystem;

  // startup script
  const [startupScript, setStartupScript] = useState<Script | null>(null);

  // Region
  const [isRegionValid, setIsRegionValid] = useState<boolean>(false);
  // Operating System
  const [isOperatingSystemValid, setIsOperatingSystemValid] =
    useState<boolean>(false);

  const [amiUrl, setAmiUrl] = useState("");
  const { data: azureAmisList } = useGetProvidersAzureAmis(
    selectedAccount?.display_name || "", // tenantID
    selectedRegion, // region
    String(selectedCloudInstanceType),
    {
      query: {
        queryKey: getGetProvidersAzureAmisQueryKey(
          selectedAccount?.display_name || "",
          selectedRegion,
          String(selectedCloudInstanceType),
        ),
        enabled: isAzure && !!selectedRegion && !!selectedAccount?.display_name,
      },
    },
  );

  const normalizedAmis: NormalizedAMI[] = useMemo(() => {
    if (isAWS && operatingSystemsList?.amis) {
      return operatingSystemsList.amis.map((ami) => ({
        name: ami.name,
        description: ami.name, // or ami.description if it exists
        image_id: ami.image_id,
      }));
    }
    if (isAzure && azureAmisList?.amis) {
      return azureAmisList.amis.map((ami) => ({
        name: ami.name,
        description: `${ami.publisher} ${ami.sku}`,
        image_id: ami.version,
        publisher: ami.publisher,
        offer: ami.offer,
        sku: ami.sku,
        version: ami.version,
      }));
    }

    return [];
  }, [isAWS, isAzure, operatingSystemsList, azureAmisList]);

  const primaryButton: ButtonProps = {
    label: "Create",
    onClick: () => handleCreateCloudInstanceDeployment(),
    variant: "primary",
    disabled: !areInputsValid() || isBlocked,
  };

  const secondaryButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      closeModal();
      setNodeType(null);
      setCreateNodeScreen(CreateNodeScreen.SelectNodeType);
    },
    variant: "secondary",
  };

  /**
   * Handle a change in the cloud account dropdown, reset all dependent selections.
   * @param cloudAccount The selected cloud account (includes provider)
   */
  function handleAccountDropdownChange(cloudAccount: CloudAccount) {
    setSelectedAccount(cloudAccount); // replaces setSelectedProvider
    setSelectedOperatingSystem(null);
    setIsOperatingSystemValid(false);
    setSelectedCloudInstanceType(null);
    setIsSizeValid(false);
    setSelectedRegion("");
    setIsRegionValid(false);
    setAmiUrl("");
    setInstanceTypeUrl("");
    setSizeQuery("");
  }

  // Handle a change in the region dropdown, fetch available AMIs and set them in the operating system dropdown.

  function handleRegionDropdownChange(region: string) {
    setSelectedRegion(region);
    setIsRegionValid(true);
    setSelectedOperatingSystem(null);
    setIsOperatingSystemValid(false);
    setSelectedCloudInstanceType(null);
    setIsSizeValid(false);
    setAmiUrl("");
    setInstanceTypeUrl("");
    setSizeQuery("");
  }

  function handleOperatingSystemDropdownChange(operatingSystem: AMI | null) {
    setSelectedOperatingSystem(operatingSystem);
    setIsOperatingSystemValid(!!operatingSystem);

    // Reset instance type when OS changes
    setSelectedCloudInstanceType(null);
    setIsSizeValid(false);
    setSizeQuery("");
  }

  function handleInstanceTypeChange(instanceType: CloudInstanceType | null) {
    setSelectedCloudInstanceType(instanceType);
    setIsSizeValid(!!instanceType);
    setSizeQuery(""); // Clear the search query when an instance type is selected
  }

  function handlePortsChange(e: React.ChangeEvent<HTMLInputElement>) {
    const portValue = parseInt(e.target.value, 10); // Convert string value to number
    if (e.target.checked) {
      setSelectedPorts((prevPorts) => [...prevPorts, portValue]);
    } else {
      setSelectedPorts((prevPorts) =>
        prevPorts.filter((port) => port !== portValue),
      );
    }
  }
  const azureVmSizeDocUrls: Record<string, string> = {
    // General-purpose families
    standard_a:
      "https://learn.microsoft.com/en-us/azure/virtual-machines/sizes/general-purpose/a-series",
    standard_b:
      "https://learn.microsoft.com/en-us/azure/virtual-machines/sizes/general-purpose/b-family",
    standard_d:
      "https://learn.microsoft.com/en-us/azure/virtual-machines/sizes/general-purpose/d-family",
    standard_ds:
      "https://learn.microsoft.com/en-us/azure/virtual-machines/sizes/general-purpose/d-family", // DS is variant of D

    // Memory optimized
    standard_e:
      "https://learn.microsoft.com/en-us/azure/virtual-machines/sizes/memory-optimized/e-family",
    standard_es:
      "https://learn.microsoft.com/en-us/azure/virtual-machines/sizes/memory-optimized/e-family",

    // Compute optimized
    standard_f:
      "https://learn.microsoft.com/en-us/azure/virtual-machines/sizes/compute-optimized/f-family",
    standard_fsv2:
      "https://learn.microsoft.com/en-us/azure/virtual-machines/sizes/compute-optimized/fsv2-series", // more precise

    // Storage optimized
    standard_l:
      "https://learn.microsoft.com/en-us/azure/virtual-machines/sizes/storage-optimized/lsv2-series",

    // GPU optimized (examples)
    standard_nc:
      "https://learn.microsoft.com/en-us/azure/virtual-machines/sizes/gpu/nc-series",
    standard_nd:
      "https://learn.microsoft.com/en-us/azure/virtual-machines/sizes/gpu/nd-series",

    // Specialized or other
    standard_h:
      "https://learn.microsoft.com/en-us/azure/virtual-machines/sizes/high-performance/h-series",
  };

  function getAzureVmSizeDocUrl(vmSize: string): string {
    const sizePrefix = vmSize.toLowerCase();

    // Try exact match or prefix match from longest to shortest
    for (const prefix of Object.keys(azureVmSizeDocUrls).sort(
      (a, b) => b.length - a.length,
    )) {
      if (sizePrefix.startsWith(prefix)) {
        return azureVmSizeDocUrls[prefix];
      }
    }

    // Default fallback
    return "https://learn.microsoft.com/en-us/azure/virtual-machines/sizes";
  }

  function getDebianVersionUrl(amiNameOrDescription: string): string {
    const lower = amiNameOrDescription.toLowerCase();

    if (lower.includes("debian-11")) {
      return "https://www.debian.org/releases/bullseye/";
    }
    if (lower.includes("debian-12")) {
      return "https://www.debian.org/releases/bookworm/";
    } else {
      return "https://www.debian.org/releases";
    }
  }

  useEffect(() => {
    if (isAWS && selectedRegion && selectedOperatingSystem) {
      setAmiUrl(
        `https://${selectedRegion}.console.aws.amazon.com/ec2/home?region=${selectedRegion}#ImageDetails:imageId=${selectedOperatingSystem.image_id}`,
      );
    }
    if (isAWS && selectedRegion && selectedCloudInstanceType) {
      setInstanceTypeUrl(
        `https://${selectedRegion}.console.aws.amazon.com/ec2/home?region=${selectedRegion}#InstanceTypeDetails:instanceType=${selectedCloudInstanceType.type}`,
      );
    }
    if (isAzure && selectedOperatingSystem) {
      const azureAmi = selectedOperatingSystem as AmiAzure;
      setAmiUrl(getDebianVersionUrl(azureAmi.offer));
    }

    if (isAzure && selectedCloudInstanceType) {
      setInstanceTypeUrl(getAzureVmSizeDocUrl(selectedCloudInstanceType.type));
    }
  }, [selectedRegion, selectedOperatingSystem, selectedCloudInstanceType]);

  function areInputsValid() {
    return (
      isSizeValid && isRegionValid && isOperatingSystemValid && name.length > 0
    );
  }

  const createCloudInstanceDeploymentMutation = useMutation({
    mutationFn: (formData: PostNodesCloudInstanceMutationBody) =>
      postNodesCloudInstance(formData),
    onError: () => {
      openErrorModal({
        title: "Error creating node instance.",
        message: "Please try again later.",
      });
    },
    onSettled: (_data: any, error: unknown) => {
      if (!error) {
        toast.success(
          "Request for creating node instance is successfully submitted.",
        );
        triggerCooldown(60);
      } else if ((error as any)?.response?.status === 429) {
        const retryAfter =
          Number((error as any)?.response?.headers?.["retry-after"]) || 60;
        triggerCooldown(retryAfter);
      }
      const queryKey = getGetEngagementGraphsQueryKey(engagementID);
      queryClient.invalidateQueries({ queryKey });
      closeModal();
    },
  });

  const handleCreateCloudInstanceDeployment = () => {
    if (!selectedAccount || !selectedOperatingSystem) return;

    const isAzure = selectedAccount.provider === "AZURE";
    const isAWS = selectedAccount.provider === "AWS";

    if (isAWS) {
      const formData: PostNodesCloudInstanceMutationBody = {
        engagement_id: engagementID,
        selected_account_id: selectedAccount.id,
        provider: "AWS",
        region: selectedRegion,
        name,
        open_ingress_tcp_ports: ports,
        startup_script: startupScript?.content
          ? btoa(startupScript.content)
          : undefined,
        instance_type: selectedCloudInstanceType?.type || " ",
        node_group_id: nodeGroupID,
        operating_system_image_id: selectedOperatingSystem.image_id,
      };

      createCloudInstanceDeploymentMutation.mutate(formData);
    }

    if (isAzure) {
      const ami = selectedOperatingSystem as AmiAzure;
      const formData: PostNodesCloudInstanceMutationBody = {
        engagement_id: engagementID,
        selected_account_id: selectedAccount.id,
        provider: "AZURE",
        region: selectedRegion,
        name,
        open_ingress_tcp_ports: ports,
        startup_script: startupScript?.content
          ? btoa(startupScript.content)
          : undefined,
        instance_type: selectedCloudInstanceType?.type || " ",
        node_group_id: nodeGroupID,
        operating_system_image: {
          publisher: ami.publisher,
          offer: ami.offer,
          sku: ami.sku,
          version: ami.version,
        },
      };

      createCloudInstanceDeploymentMutation.mutate(formData);
    }
  };

  // user scripts
  const { accounts } = useMsal();
  const userId = accounts.length > 0 ? accounts[0].localAccountId : "";
  const { data: userScriptsResponse } = useQuery({
    queryKey: getGetUserScriptsQueryKey(userId),
    queryFn: () => getUserScripts(userId),
  });
  const userScripts = userScriptsResponse?.scripts || [];

  // admin scripts
  const { data: adminScriptsResponse } = useQuery({
    queryKey: getGetAdminScriptsQueryKey(),
    queryFn: () => getAdminScripts(),
  });
  const adminScripts = adminScriptsResponse?.scripts || [];

  // Concatenate user and admin scripts in one array
  const allScripts: Script[] = [...userScripts, ...adminScripts];

  const handleScriptSelection = (selectedScriptId: string) => {
    const selectedScript = allScripts.find(
      (script: Script) => script.id === selectedScriptId,
    );
    if (selectedScript) {
      setStartupScript(selectedScript);
    }
  };

  function clearValues() {
    setStartupScript(null);
  }

  function openErrorModal(errorMessage: ErrorMessage) {
    if (errorMessage) {
      setErrorMessage(errorMessage);
    }
    setIsOpenErrorModal(true);
  }

  function closeErrorModal() {
    setIsOpenErrorModal(false);
  }

  return (
    <>
      <div className="mx-auto flex w-full flex-col rounded-lg pt-4 md:p-4 xl:p-8 dark:text-white">
        <div
          id="node-type-details"
          className="space-y-4 md:grid lg:grid-cols-2 lg:gap-6 lg:space-y-0"
        >
          {/* Provider Input */}
          <div id="provider-input" className="flex flex-col">
            <label className="flex flex-row space-x-1 pb-1">
              <span className="font-semibold">AWS Account/Azure Tenant</span>
              <span className="text-red-600">*</span>
            </label>
            <Listbox
              value={selectedAccount ?? undefined}
              onChange={handleAccountDropdownChange}
            >
              <div className="relative">
                <ListboxButton className="h-11 w-full rounded-sm border border-gray-400 px-4 py-2 text-left text-black dark:text-white">
                  <div className="flex items-center justify-between">
                    <span className={selectedAccount ? "" : "text-gray-400"}>
                      {selectedAccount
                        ? `${selectedAccount.display_name}`
                        : "Select a Cloud Account or Tenant"}
                    </span>
                    <HiChevronDown className="h-4 w-4" />
                  </div>
                </ListboxButton>

                <ListboxOptions className="scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-transparent absolute z-50 mt-1 max-h-64 w-full overflow-y-auto rounded-xl border border-white/5 bg-white p-1 shadow-lg dark:bg-slate-500">
                  {["AWS", "AZURE"].map((provider) => {
                    const accounts = cloudAccountsData?.accounts?.filter(
                      (acc) => acc.provider === provider,
                    );

                    if (!accounts?.length) return null;

                    return (
                      <div key={provider} className="mb-2">
                        <div className="border-b border-gray-200 px-3 py-1 text-sm font-semibold text-purple-700 dark:border-gray-700 dark:text-purple-300">
                          {provider === "AWS"
                            ? "=== AWS Accounts ==="
                            : "=== Azure Tenants ==="}
                        </div>
                        {accounts.map((account) => (
                          <ListboxOption
                            key={account.id}
                            value={account}
                            className={({ active, selected }) =>
                              `group flex cursor-pointer items-center gap-2 rounded-lg px-3 py-2 text-sm ${selected ? "bg-purple-100 font-semibold dark:bg-purple-700" : ""} ${active && !selected ? "bg-purple-50 dark:bg-gray-800" : "text-black dark:text-white"}`
                            }
                          >
                            <span>{account.display_name}</span>
                          </ListboxOption>
                        ))}
                      </div>
                    );
                  })}
                </ListboxOptions>
              </div>
            </Listbox>
          </div>
          <div id="region-input" className="flex flex-col">
            <label className="flex flex-row space-x-1 pb-1">
              <span className="font-semibold">Region</span>
              <span className="text-red-600">*</span>
            </label>

            <Combobox
              value={selectedRegion}
              onChange={(region: string | null) => {
                // In our implementation, region should never be null since all options provide string values
                if (!region) return;
                handleRegionDropdownChange(region);
              }}
            >
              <div className="relative">
                <div className="relative w-full">
                  <ComboboxInput
                    className="h-11 w-full rounded-sm border border-gray-400 px-4 py-2 text-left text-black dark:text-white"
                    displayValue={() => selectedRegion}
                    onChange={(event) => setRegionQuery(event.target.value)}
                    placeholder="Select a region..."
                    onFocus={(e) => {
                      e.target.placeholder = "";
                    }}
                    onBlur={(e) => {
                      if (!selectedRegion) {
                        e.target.placeholder = "Select a region";
                      }
                    }}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2">
                    <ComboboxButton>
                      <HiChevronDown className="h-4 w-4" />
                    </ComboboxButton>
                  </div>
                </div>
                <ComboboxOptions className="scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-transparent absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-xl border border-white/5 bg-white p-1 shadow-lg focus:outline-hidden dark:bg-slate-500">
                  {(isAWS ? regions : azureRegionsList?.regions || [])
                    .filter(
                      (region) =>
                        !regionQuery ||
                        region
                          .toLowerCase()
                          .includes(regionQuery.toLowerCase()),
                    )
                    .map((region: string) => (
                      <ComboboxOption
                        key={region}
                        value={region}
                        className={({ active }) =>
                          `${active ? "bg-purple-600 text-white" : "text-black dark:text-white"} relative cursor-pointer rounded-lg px-3 py-2 select-none`
                        }
                      >
                        {region}
                      </ComboboxOption>
                    ))}
                </ComboboxOptions>
              </div>
            </Combobox>
          </div>

          <div className="flex flex-row">
            <div id="operating-system-input" className="flex w-full flex-col">
              <label className="flex flex-row space-x-1 pb-1">
                <span className="font-semibold">OS</span>
                <span className="text-red-600">*</span>
              </label>
              <Listbox
                value={selectedOperatingSystem}
                onChange={(e: AMI | null) =>
                  handleOperatingSystemDropdownChange(e)
                }
              >
                <ListboxButton
                  className={`w-full rounded-sm border border-gray-400 px-4 py-2 text-left text-gray-700 dark:text-white ${!selectedOperatingSystem ? "min-h-[48px]" : ""}`}
                >
                  <div className="flex flex-row items-center justify-between">
                    <span>
                      {selectedOperatingSystem
                        ? selectedOperatingSystem.description
                        : "-"}
                    </span>
                    <HiChevronDown className="h-4 w-4" />
                  </div>
                </ListboxButton>
                <ListboxOptions
                  anchor="bottom"
                  className="w-[var(--button-width)] rounded-xl border border-white/5 bg-white p-1 shadow-lg [--anchor-gap:var(--spacing-1)] focus:outline-hidden dark:bg-slate-500"
                >
                  {normalizedAmis.map((operatingSystem) => (
                    <ListboxOption
                      className="group flex cursor-pointer items-center gap-2 rounded-lg px-3 py-1.5 text-black select-none hover:bg-purple-50 dark:text-white dark:hover:bg-gray-800"
                      key={operatingSystem.name}
                      value={operatingSystem}
                    >
                      <span className="block">
                        {operatingSystem.description}
                      </span>
                    </ListboxOption>
                  ))}
                </ListboxOptions>
              </Listbox>
            </div>
          </div>
          <div id="size-input" className="flex flex-col">
            <label className="flex flex-row space-x-1 pb-1">
              <span className="font-semibold">Size</span>
              <span className="text-red-600">*</span>
              {isLoadingInstanceTypes && (
                <div className="ml-2 flex items-center">
                  <div className="h-3 w-3 animate-spin rounded-full border-2 border-purple-600 border-t-transparent"></div>
                </div>
              )}
            </label>
            <Combobox
              value={selectedCloudInstanceType}
              onChange={(instanceType: CloudInstanceType | null) =>
                handleInstanceTypeChange(instanceType)
              }
              disabled={!isInstanceTypeSelectionEnabled}
            >
              <div className="relative">
                <div className="relative w-full">
                  <ComboboxInput
                    className={`h-11 w-full rounded-sm border border-gray-400 px-4 py-2 text-left ${
                      !isInstanceTypeSelectionEnabled
                        ? "cursor-not-allowed bg-gray-100"
                        : ""
                    } text-black dark:text-white`}
                    displayValue={(instanceType: CloudInstanceType | null) =>
                      instanceType ? `${instanceType.alias}` : ""
                    }
                    onChange={(event) => setSizeQuery(event.target.value)}
                    placeholder={
                      !isInstanceTypeSelectionEnabled
                        ? "Select an OS first..."
                        : isLoadingInstanceTypes
                          ? "Loading sizes..."
                          : "Select a size..."
                    }
                    onFocus={(e) => {
                      if (
                        isInstanceTypeSelectionEnabled &&
                        !isLoadingInstanceTypes
                      ) {
                        e.target.placeholder = "";
                      }
                    }}
                    onBlur={(e) => {
                      if (!selectedCloudInstanceType) {
                        e.target.placeholder = !isInstanceTypeSelectionEnabled
                          ? "Select an OS first..."
                          : "Select a size...";
                      }
                    }}
                  />
                  <div className="absolute inset-y-0 right-0 flex items-center pr-2">
                    <ComboboxButton>
                      <HiChevronDown className="h-4 w-4" />
                    </ComboboxButton>
                  </div>
                </div>
                <ComboboxOptions className="scrollbar-thin scrollbar-thumb-gray-400 scrollbar-track-transparent absolute z-50 mt-1 max-h-60 w-full overflow-auto rounded-xl border border-white/5 bg-white p-1 shadow-lg focus:outline-hidden dark:bg-slate-500">
                  {filteredInstanceTypes.length === 0 && sizeQuery !== "" ? (
                    <div className="relative cursor-default px-4 py-2 text-gray-700 select-none dark:text-gray-300">
                      No sizes found.
                    </div>
                  ) : (
                    filteredInstanceTypes.map(
                      (availableInstanceType: CloudInstanceType) => (
                        <ComboboxOption
                          key={availableInstanceType.type}
                          value={availableInstanceType}
                          className={({ active }) =>
                            `${active ? "bg-purple-600 text-white" : "text-black dark:text-white"} relative flex cursor-pointer items-center justify-between rounded-lg px-3 py-2 select-none`
                          }
                        >
                          <span className="flex-1">
                            {availableInstanceType?.alias}
                          </span>
                          <span className="text-gray-500 dark:text-gray-400">
                            {availableInstanceType.type}
                          </span>
                        </ComboboxOption>
                      ),
                    )
                  )}
                </ComboboxOptions>
              </div>
            </Combobox>
          </div>
          <a href={amiUrl} target="_blank" rel="noopener noreferrer">
            <div className="group relative mt-4 flex flex-col md:mt-0">
              <button
                disabled={!amiUrl}
                className={`${
                  !amiUrl ? "bg-gray-400" : "bg-purple-700"
                } rounded-md p-3 text-white`}
                data-tooltip-target="tooltip-top"
                data-tooltip-placement="top"
              >
                <span>{selectedAccount?.provider ?? "Cloud"} URL</span>
              </button>
              {amiUrl && (
                <span className="absolute top-10 scale-0 rounded-sm bg-gray-800 p-2 text-xs text-white group-hover:scale-100">
                  {amiUrl}
                </span>
              )}
            </div>
          </a>

          <a href={instanceTypeUrl} target="_blank">
            <div className="group relative mt-4 flex flex-col md:mt-0">
              <button
                onClick={handleCreateCloudInstanceDeployment}
                disabled={!instanceTypeUrl}
                className={`${
                  !instanceTypeUrl ? "bg-gray-400" : "bg-purple-700"
                } rounded-md p-3 text-white`}
              >
                <span>{selectedAccount?.provider ?? "Cloud"} URL</span>
              </button>
              {instanceTypeUrl && (
                <span className="absolute top-10 scale-0 rounded-sm bg-gray-800 p-2 text-xs text-white group-hover:scale-100">
                  {instanceTypeUrl}
                </span>
              )}
            </div>
          </a>
          <div id="name-input" className="flex w-full flex-col">
            <label className="flex w-full flex-row space-x-1 pb-1">
              <span className="font-semibold">Name</span>
              <span className="text-red-600">*</span>
            </label>
            <input
              className="w-full rounded-sm border border-gray-400 px-4 py-2 text-left text-black focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
              placeholder="Enter name here"
              value={name}
              onChange={(e) => {
                setName(e.target.value);
                const isAzure = selectedAccount?.provider === "AZURE";
                const validationResult = isAzure
                  ? validationSchemaType.azureInstanceName.safeParse(
                      e.target.value,
                    )
                  : validationSchemaType.instanceName.safeParse(e.target.value);

                if (!validationResult.success) {
                  setError(getZodErrorMessage(validationResult.error));
                } else {
                  setError("");
                }
              }}
              type="text"
            />
            {error && <p className="text-red-500">{error}</p>}
          </div>
          <div className="hidden lg:max-xl:flex" />
          <div id="security-groups" className="flex flex-col justify-between">
            <label className="flex flex-row space-x-1">
              <span className="font-semibold">Security Groups</span>
            </label>
            <div
              id="port-selections"
              className="flex flex-col text-gray-600 md:flex-row md:space-x-4 lg:w-max dark:text-white"
            >
              <div className="flex-row items-center space-x-1">
                <input
                  id="ssh_key"
                  type="checkbox"
                  value={22}
                  checked={ports.includes(22)}
                  onChange={handlePortsChange}
                />
                <label htmlFor="ssh_key">ssh</label>
              </div>
              <div className="flex-row items-center space-x-1">
                <input
                  id="http_selection"
                  type="checkbox"
                  value={80}
                  onChange={handlePortsChange}
                />
                <label htmlFor="http_selection">http</label>
              </div>
              <div className="flex-row items-center space-x-1">
                <input
                  id="https_selection"
                  type="checkbox"
                  value={443}
                  onChange={handlePortsChange}
                />
                <label htmlFor="https_selection">https</label>
              </div>
              <div className="flex flex-row items-center space-x-1">
                <input
                  id="udp-53-selection"
                  type="checkbox"
                  value={53}
                  onChange={handlePortsChange}
                />
                <label htmlFor="udp-53-selection">udp_53</label>
              </div>
              <div className="flex-row items-center space-x-1">
                <input
                  id="udp-9100-selection"
                  type="checkbox"
                  value={9100}
                  onChange={handlePortsChange}
                />
                <label htmlFor="udp-9100-selection">udp_9100</label>
              </div>
            </div>
          </div>
        </div>
        <div id="size-input" className="flex flex-col">
          <label className="flex flex-row space-x-1 pt-4 pb-1">
            <span className="font-semibold">Custom Script</span>
          </label>
          <Listbox
            value={startupScript?.description || ""}
            onChange={handleScriptSelection}
          >
            <ListboxButton className="h-11 w-full rounded-sm border border-gray-400 px-4 py-2 text-left text-black focus:outline-hidden dark:text-white">
              <div className="flex flex-row items-center justify-between">
                <span>{startupScript?.name}</span>
                <div className="flex items-center">
                  {startupScript && (
                    <div onClick={clearValues} className="ml-2">
                      <HiXMark className="h-5 w-5 text-black" />
                    </div>
                  )}
                  {startupScript ? null : (
                    <HiChevronDown className="ml-2 h-4 w-4" />
                  )}
                </div>
              </div>
            </ListboxButton>
            <ListboxOptions
              anchor="bottom"
              className="w-[var(--button-width)] rounded-xl border border-white/5 bg-white p-1 shadow-lg [--anchor-gap:var(--spacing-1)] focus:outline-hidden dark:bg-slate-500"
            >
              <div className="max-h-40 overflow-y-auto">
                <div className="my-2 cursor-default text-center font-bold">
                  --------- Admin Scripts ---------
                </div>
                {adminScripts.map((customScript: Script) => (
                  <ListboxOption
                    className="group flex cursor-pointer items-center gap-2 rounded-lg px-3 py-1.5 text-black select-none hover:bg-purple-50 dark:text-white dark:hover:bg-gray-800"
                    key={customScript.id}
                    value={customScript.id}
                  >
                    <RiAdminLine className="mr-1 h-4 w-4 text-sky-600" />
                    <span className="font-semibold">
                      {customScript.name}
                    </span> - {customScript.description}
                  </ListboxOption>
                ))}
                <div className="my-2 cursor-default text-center font-bold">
                  --------- User Scripts ---------
                </div>
                {userScripts &&
                  userScripts.map((customScript: Script) => (
                    <ListboxOption
                      className="group flex cursor-pointer items-center gap-2 rounded-lg px-3 py-1.5 text-black select-none hover:bg-purple-50 dark:text-white dark:hover:bg-gray-800"
                      key={customScript.id}
                      value={customScript.id}
                    >
                      <RiUserLine className="mr-1 h-4 w-4 text-purple-600" />
                      <span className="font-semibold">
                        {customScript.name}
                      </span>{" "}
                      - {customScript.description}
                    </ListboxOption>
                  ))}
              </div>
            </ListboxOptions>
          </Listbox>
        </div>
        <div id="startup-script-option" className="flex flex-col space-y-1">
          <label className="flex flex-row space-x-1 pt-4">
            <span className="font-semibold">Script</span>
          </label>
          <div>
            <HighlightedTextarea value={startupScript?.content} />
          </div>
        </div>
        <div
          id="node-selection-actions"
          className="flex flex-col items-center justify-between align-middle md:flex-row"
        >
          <button
            className="flex hidden flex-row md:flex"
            onClick={() => {
              setCreateNodeScreen(CreateNodeScreen.SelectNodeType);
              setNodeType(null);
            }}
          >
            <div className="flex flex-row items-center space-x-2">
              <HiChevronLeft className="h-4 w-4 dark:text-white" />
              <span>Back</span>
            </div>
          </button>
          <div className="flex w-full flex-row items-center justify-end space-x-4 pt-4 align-middle">
            {isBlocked && (
              <span className="mr-4 text-sm whitespace-nowrap text-gray-400">
                Available in 00:{cooldown.toString().padStart(2, "0")}
              </span>
            )}
            <ActionButtons
              primaryButton={primaryButton}
              secondaryButton={secondaryButton}
            />
          </div>
        </div>
      </div>
      <ErrorMessageModal
        isOpen={isOpenErrorModal}
        closeModal={closeErrorModal}
        errorMessage={errorMessage}
      />
    </>
  );
}
