import { RankingInfo, rankItem } from "@tanstack/match-sorter-utils";
import {
  Column,
  ColumnDef,
  ColumnFiltersState,
  ExpandedState,
  FilterFn,
  Row,
  SortingState,
  flexRender,
  getCoreRowModel,
  getExpandedRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table";
import { Calendar } from "primereact/calendar";
import { useEffect, useState } from "react";
import {
  HiChevronDown,
  HiChevronLeft,
  HiChevronRight,
  HiChevronUp,
} from "react-icons/hi";
import { HiChevronUpDown, HiMagnifyingGlass, HiXMark } from "react-icons/hi2";
import { TbFilterOff } from "react-icons/tb";

import { useTheme } from "../context/ThemeProvider";

interface TableProps<T> {
  data: T[];
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  columns: ColumnDef<T, any>[];
  getSubRows?: (row: T) => T[] | undefined;
  subRowLen?: number;
  // Optional callback to expose selected rows
  onRowSelectionChange?: (selectedRows: Row<T>[]) => void;
  // Optional ref to expose table methods
  tableRef?: React.MutableRefObject<{ clearSelection: () => void } | null>;
}

declare module "@tanstack/react-table" {
  interface FilterFns {
    fuzzy: FilterFn<unknown>;
    dateRange: FilterFn<unknown>;
  }
  interface FilterMeta {
    itemRank: RankingInfo;
    filterVariant?: string;
  }
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  interface ColumnMeta<TData, TValue> {
    getRowClassName?: (row: Row<TData>) => string;
    filterVariant?: string;
  }
}

// Fuzzy filter function
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const fuzzyFilter: FilterFn<any> = (row, columnId, value, addMeta) => {
  const itemRank = rankItem(row.getValue(columnId), value);
  addMeta({ itemRank });
  return itemRank.passed;
};
function Filter({ column }: { column: Column<any, unknown> }) {
  // Check if this is a date column
  const isDateColumn = column.columnDef.meta?.filterVariant === "date";

  if (isDateColumn) {
    return <DateFilter column={column} />;
  }

  const columnFilterValue = column.getFilterValue();
  return (
    <DebouncedInput
      type="text"
      value={(columnFilterValue ?? "") as string}
      onChange={(value) => column.setFilterValue(value)}
      placeholder={`Search...`}
      className="mt-1 w-5/6 rounded-sm border border-gray-300 bg-transparent p-2 focus:ring-2 focus:ring-purple-500 focus:outline-hidden dark:border-slate-50"
    />
  );
}

function DateFilter({ column }: { column: Column<any, unknown> }) {
  const columnFilterValue = column.getFilterValue() as
    | [Date?, Date?]
    | undefined;
  const [startDate, endDate] = columnFilterValue || [undefined, undefined];

  return (
    <div className="table-filter-container mt-1 flex w-full max-w-full min-w-0 flex-col gap-1">
      <Calendar
        value={startDate || null}
        onChange={(e) => {
          const newStart = e.value as Date | null;
          column.setFilterValue([newStart || undefined, endDate]);
        }}
        dateFormat="dd.mm.yy"
        placeholder="From"
        className="w-full text-xs"
        inputClassName="w-full min-w-0 max-w-full rounded-sm border border-gray-300 bg-transparent p-1 text-xs focus:outline-hidden focus:ring-1 focus:ring-purple-500 dark:border-slate-50 sm:text-xs"
        showIcon={false}
        showButtonBar={false}
        panelClassName="text-xs z-50"
        style={{ fontSize: "12px", width: "100%" }}
        appendTo="self"
        touchUI={false}
      />
      <Calendar
        value={endDate || null}
        onChange={(e) => {
          const newEnd = e.value as Date | null;
          column.setFilterValue([startDate, newEnd || undefined]);
        }}
        dateFormat="dd.mm.yy"
        placeholder="To"
        className="w-full text-xs"
        inputClassName="w-full min-w-0 max-w-full rounded-sm border border-gray-300 bg-transparent p-1 text-xs focus:outline-hidden focus:ring-1 focus:ring-purple-500 dark:border-slate-50 sm:text-xs"
        showIcon={false}
        showButtonBar={false}
        panelClassName="text-xs z-50"
        style={{ fontSize: "12px", width: "100%" }}
        appendTo="self"
        touchUI={false}
      />
    </div>
  );
}

// Add custom date range filter function
const dateRangeFilter: FilterFn<any> = (row, columnId, value) => {
  const [startDate, endDate] = value || [undefined, undefined];
  const cellValue = row.getValue(columnId);

  if (!cellValue) return true;

  // Handle different date formats
  let rowDate: Date;
  if (typeof cellValue === "string") {
    // Handle DD.MM.YYYY format
    if (cellValue.includes(".") && cellValue.split(".").length === 3) {
      const parts = cellValue.split(".");
      if (parts.length === 3 && parts[2].length === 4) {
        // Convert DD.MM.YYYY to YYYY-MM-DD for Date constructor
        rowDate = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`);
      } else {
        rowDate = new Date(cellValue);
      }
    } else {
      rowDate = new Date(cellValue);
    }
  } else {
    rowDate = new Date(cellValue as string | number | Date);
  }

  if (isNaN(rowDate.getTime())) return true;

  if (startDate && rowDate < startDate) return false;
  if (endDate && rowDate > endDate) return false;

  return true;
};

export function DebouncedInput({
  value: initialValue,
  onChange,
  debounce = 500,
  ...props
}: {
  value: string | number;
  onChange: (value: string | number) => void;
  debounce?: number;
} & Omit<React.InputHTMLAttributes<HTMLInputElement>, "onChange">) {
  const [value, setValue] = useState(initialValue);

  useEffect(() => {
    setValue(initialValue);
  }, [initialValue]);

  useEffect(() => {
    const timeout = setTimeout(() => onChange(value), debounce);
    return () => clearTimeout(timeout);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [value]);

  return (
    <input
      {...props}
      value={value}
      onChange={(e) => setValue(e.target.value)}
    />
  );
}

function Table<T>({ data, columns, getSubRows, subRowLen = 0, onRowSelectionChange, tableRef }: TableProps<T>) {
  const [sorting, setSorting] = useState<SortingState>([]);
  const [expanded, setExpanded] = useState<ExpandedState>({});
  const [columnFilters, setColumnFilters] = useState<ColumnFiltersState>([]);
  const [globalFilter, setGlobalFilter] = useState("");
  const [rowSelection, setRowSelection] = useState({});
  const [pagination, setPagination] = useState({
    pageIndex: 0,
    pageSize: 10,
  });

  const table = useReactTable<T>({
    data,
    columns,
    filterFns: {
      fuzzy: fuzzyFilter,
      dateRange: dateRangeFilter,
    },
    state: { sorting, expanded, columnFilters, globalFilter, pagination, rowSelection },
    globalFilterFn: fuzzyFilter,
    getExpandedRowModel: getExpandedRowModel(),
    onColumnFiltersChange: setColumnFilters,
    onGlobalFilterChange: setGlobalFilter,
    getCoreRowModel: getCoreRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    onPaginationChange: setPagination,
    getSortedRowModel: getSortedRowModel(),
    onSortingChange: setSorting,
    onExpandedChange: setExpanded,
    onRowSelectionChange: setRowSelection,
    getSubRows,
    enableRowSelection: true,
  });

  const { isDarkMode } = useTheme();

  // Expose selected rows to parent
  useEffect(() => {
    onRowSelectionChange?.(table.getSelectedRowModel().flatRows as Row<T>[]);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [table.getState().rowSelection]);

  // Expose table methods to parent via ref
  useEffect(() => {
    if (tableRef) {
      tableRef.current = {
        clearSelection: () => {
          setRowSelection({});
        },
      };
    }
  }, [tableRef]);

  // Function to reset all filters
  const resetAllFilters = () => {
    setGlobalFilter("");
    setColumnFilters([]);
    table.resetColumnFilters();
    table.resetGlobalFilter();
  };

  // Check if any filters are active
  const hasActiveFilters = globalFilter !== "" || columnFilters.length > 0;

  useEffect(() => {
    setPagination((prevPagination) => ({
      ...prevPagination,
      pageSize: prevPagination.pageSize + subRowLen,
    }));
  }, [subRowLen]);

  return (
    <div className="mt-2 sm:mt-4 md:p-2">
      <div className="mb-4 flex flex-col md:flex-row md:items-center md:justify-between">
        <div className="top-1 flex w-full flex-row items-center rounded-sm border border-solid border-gray-400 bg-white px-4 focus-within:ring-2 focus-within:ring-purple-500 focus-within:outline-hidden md:mt-3 md:mb-0 md:max-w-sm xl:max-w-lg dark:bg-[#303c4f]">
          <HiMagnifyingGlass
            className={`${isDarkMode ? "text-white" : "text-black"} h-5 w-5`}
          />
          <DebouncedInput
            value={globalFilter ?? ""}
            onChange={(value) => setGlobalFilter(String(value))}
            className="font-lg w-full bg-transparent p-2 focus:ring-0 focus:outline-hidden dark:text-white"
            placeholder="Search all columns..."
          />
          {globalFilter && (
            <button
              onClick={() => setGlobalFilter("")}
              className="text-gray-400 hover:text-gray-600 dark:text-gray-300 dark:hover:text-white"
            >
              <HiXMark className="h-5 w-5" />
            </button>
          )}
        </div>

        {hasActiveFilters && (
          <button
            onClick={resetAllFilters}
            className="mt-2 flex items-center space-x-1 rounded-md border border-gray-300 px-3 py-2 transition-colors hover:bg-gray-100 md:mt-0 dark:border-gray-600 dark:text-white dark:hover:bg-gray-700"
            title="Reset all filters"
          >
            <TbFilterOff className="h-4 w-4" />
            <span>Reset Filters</span>
          </button>
        )}
      </div>
      <div className="relative mt-4 w-full overflow-x-hidden rounded-md border border-gray-100 dark:border-gray-700">
        <table className="w-full table-fixed whitespace-normal">
          <thead className="sticky top-0 z-10 bg-white dark:bg-[#303c4f] dark:text-white">
            {table.getHeaderGroups().map((headerGroup) => (
              <tr key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <th
                    key={header.id}
                    className="min-w-0 px-1 pt-3 pb-2 text-left text-sm font-medium whitespace-normal break-words sm:px-3 dark:text-white"
                  >
                    {header.isPlaceholder ? null : (
                      <div
                        className={
                          header.column.getCanSort()
                            ? "flex cursor-pointer items-center justify-between"
                            : ""
                        }
                        onClick={header.column.getToggleSortingHandler()}
                        title={
                          header.column.getCanSort()
                            ? header.column.getIsSorted() === "asc"
                              ? "Sort ascending"
                              : header.column.getIsSorted() === "desc"
                                ? "Sort descending"
                                : "Clear sort"
                            : undefined
                        }
                      >
                        {flexRender(
                          header.column.columnDef.header,
                          header.getContext(),
                        )}
                        {header.column.getCanSort() &&
                          {
                            asc: <HiChevronUp className="h-4 w-4" />,
                            desc: <HiChevronDown className="h-4 w-4" />,
                            clear: (
                              <HiChevronUpDown
                                className={`${isDarkMode ? "text-white" : "text-black"} h-4 w-4`}
                              />
                            ),
                          }[header.column.getIsSorted() || "clear"]}
                      </div>
                    )}
                    {header.column.getCanFilter() && (
                      <Filter column={header.column} />
                    )}
                  </th>
                ))}
              </tr>
            ))}
          </thead>
          <tbody className="dark:text-white">
            {table.getRowModel().rows.map((row, rowIndex) => {
              // Check if any column has a getRowClassName function in its meta
              const rowBackgroundColumn = columns.find(
                (col) =>
                  col.meta && typeof col.meta.getRowClassName === "function",
              );

              // Get the background color class if the function exists
              const customBgClass =
                rowBackgroundColumn?.meta?.getRowClassName?.(row) || "";

              // Use the custom background class if available, otherwise use the default striped pattern
              const bgClass =
                customBgClass ||
                (rowIndex % 2 === 1 ? "bg-slate-50 dark:bg-slate-600" : "");

              return (
                <tr key={row.id} className={bgClass}>
                  {row.getVisibleCells().map((cell) => (
                    <td
                      key={cell.id}
                      className="min-w-0 px-1 py-2 text-left text-sm font-normal whitespace-normal break-words sm:px-3"
                    >
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext(),
                      )}
                    </td>
                  ))}
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
      <div className="flex flex-wrap items-center justify-between gap-2 pt-4">
        <div className="flex gap-2">
          <button
            onClick={() => table.previousPage()}
            disabled={!table.getCanPreviousPage()}
          >
            <HiChevronLeft
              className={`${isDarkMode ? "text-white" : "text-black"} ${!table.getCanPreviousPage() ? "text-gray-400" : "text-black dark:text-white"} h-5 w-5 cursor-pointer`}
            />
          </button>
          <div className="rounded-sm bg-purple-700 px-3 py-2 text-white dark:bg-purple-500">
            {table.getState().pagination.pageIndex + 1}
          </div>
          <button
            className={
              !table.getCanNextPage() ? "text-gray-100" : "text-grey-500"
            }
            onClick={() => table.nextPage()}
            disabled={!table.getCanNextPage()}
          >
            <HiChevronRight
              className={`${isDarkMode ? "text-white" : "text-black"} ${!table.getCanNextPage() ? "text-gray-400" : "text-black dark:text-white"} h-5 w-5 cursor-pointer`}
            />
          </button>
        </div>
        <div className="align-center">
          <span className="content-bottom text-xs font-medium text-gray-400 dark:text-gray-200">
            Showing {table.getState().pagination.pageIndex + 1} of{" "}
            {table.getPageCount()} pages
          </span>
        </div>
      </div>
    </div>
  );
}

export default Table;
