import { useEffect, useRef, useState } from "react";
import CryptoJS from "crypto-js";

interface SecureRateLimitState {
  isBlocked: boolean;
  cooldown: number;
  attempts: number;
}

/**
 * More secure client-side rate limiting using multiple storage mechanisms
 * and obfuscation. Still not foolproof but much harder to bypass.
 */
export function useSecureRateLimit(operation: string, maxAttempts: number = 3) {
  const [state, setState] = useState<SecureRateLimitState>({
    isBlocked: false,
    cooldown: 0,
    attempts: 0,
  });
  
  const timerRef = useRef<NodeJS.Timeout | null>(null);
  const secretKey = useRef<string>();
  
  // Generate a session-specific key
  useEffect(() => {
    if (!secretKey.current) {
      secretKey.current = `${operation}_${Date.now()}_${Math.random()}`;
    }
  }, [operation]);

  // Multiple storage keys for redundancy
  const getStorageKeys = () => ({
    primary: `rl_${operation}`,
    backup: `rl_bk_${CryptoJS.MD5(operation).toString()}`,
    session: `rl_ss_${operation}`,
    memory: `rl_mem_${operation}`,
  });

  // Encrypt data before storing
  const encryptData = (data: any): string => {
    return CryptoJS.AES.encrypt(JSON.stringify(data), secretKey.current!).toString();
  };

  // Decrypt data after retrieving
  const decryptData = (encrypted: string): any => {
    try {
      const bytes = CryptoJS.AES.decrypt(encrypted, secretKey.current!);
      return JSON.parse(bytes.toString(CryptoJS.enc.Utf8));
    } catch {
      return null;
    }
  };

  // Store rate limit data in multiple places
  const storeRateLimitData = (data: { until: number; attempts: number }) => {
    const keys = getStorageKeys();
    const encrypted = encryptData(data);
    
    // Store in multiple locations
    localStorage.setItem(keys.primary, encrypted);
    sessionStorage.setItem(keys.session, encrypted);
    
    // Store backup with different encryption
    const backupData = encryptData({ ...data, checksum: CryptoJS.MD5(JSON.stringify(data)).toString() });
    localStorage.setItem(keys.backup, backupData);
  };

  // Retrieve rate limit data with validation
  const getRateLimitData = (): { until: number; attempts: number } | null => {
    const keys = getStorageKeys();
    
    // Try primary storage
    const primary = localStorage.getItem(keys.primary);
    if (primary) {
      const data = decryptData(primary);
      if (data && typeof data.until === 'number' && typeof data.attempts === 'number') {
        return data;
      }
    }
    
    // Try backup storage
    const backup = localStorage.getItem(keys.backup);
    if (backup) {
      const data = decryptData(backup);
      if (data && data.checksum) {
        const { checksum, ...actualData } = data;
        const expectedChecksum = CryptoJS.MD5(JSON.stringify(actualData)).toString();
        if (checksum === expectedChecksum) {
          return actualData;
        }
      }
    }
    
    // Try session storage
    const session = sessionStorage.getItem(keys.session);
    if (session) {
      const data = decryptData(session);
      if (data && typeof data.until === 'number' && typeof data.attempts === 'number') {
        return data;
      }
    }
    
    return null;
  };

  // Clear all rate limit data
  const clearRateLimitData = () => {
    const keys = getStorageKeys();
    localStorage.removeItem(keys.primary);
    localStorage.removeItem(keys.backup);
    sessionStorage.removeItem(keys.session);
  };

  // Initialize state from storage
  useEffect(() => {
    const data = getRateLimitData();
    if (data) {
      const now = Math.floor(Date.now() / 1000);
      if (data.until > now) {
        const cooldown = data.until - now;
        setState({
          isBlocked: true,
          cooldown,
          attempts: data.attempts,
        });
        startTimer(cooldown);
      } else {
        clearRateLimitData();
      }
    }
  }, [operation]);

  const startTimer = (initialCooldown: number) => {
    if (timerRef.current) clearInterval(timerRef.current);
    
    timerRef.current = setInterval(() => {
      setState(prev => {
        const newCooldown = prev.cooldown - 1;
        if (newCooldown <= 0) {
          clearRateLimitData();
          return { ...prev, cooldown: 0, isBlocked: false };
        }
        return { ...prev, cooldown: newCooldown };
      });
    }, 1000);
  };

  const triggerCooldown = (seconds: number, incrementAttempts: boolean = true) => {
    const until = Math.floor(Date.now() / 1000) + seconds;
    const newAttempts = incrementAttempts ? state.attempts + 1 : state.attempts;
    
    // Exponential backoff based on attempts
    const adjustedSeconds = Math.min(seconds * Math.pow(2, newAttempts - 1), 300); // Max 5 minutes
    const adjustedUntil = Math.floor(Date.now() / 1000) + adjustedSeconds;
    
    storeRateLimitData({ until: adjustedUntil, attempts: newAttempts });
    
    setState({
      isBlocked: true,
      cooldown: adjustedSeconds,
      attempts: newAttempts,
    });
    
    startTimer(adjustedSeconds);
  };

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) clearInterval(timerRef.current);
    };
  }, []);

  return {
    isBlocked: state.isBlocked,
    cooldown: state.cooldown,
    attempts: state.attempts,
    triggerCooldown,
    clearCooldown: clearRateLimitData,
  };
}
