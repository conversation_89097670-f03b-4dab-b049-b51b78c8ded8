import { useEffect, useState } from "react";
import { AxiosError } from "axios";

interface RateLimitState {
  isBlocked: boolean;
  cooldown: number;
  retryAfter?: number;
}

interface UseServerRateLimitOptions {
  operation: string;
  onRateLimited?: (retryAfter: number) => void;
}

/**
 * Enhanced rate limiting hook that relies primarily on server-side enforcement
 * with client-side UX improvements. More secure than localStorage-only approach.
 */
export function useServerRateLimit({ operation, onRateLimited }: UseServerRateLimitOptions) {
  const [state, setState] = useState<RateLimitState>({
    isBlocked: false,
    cooldown: 0,
  });

  // Client-side cooldown timer (for UX only, not security)
  useEffect(() => {
    if (state.cooldown <= 0) return;

    const timer = setInterval(() => {
      setState(prev => {
        const newCooldown = prev.cooldown - 1;
        if (newCooldown <= 0) {
          return { ...prev, cooldown: 0, isBlocked: false };
        }
        return { ...prev, cooldown: newCooldown };
      });
    }, 1000);

    return () => clearInterval(timer);
  }, [state.cooldown]);

  /**
   * Handle rate limit response from server
   */
  const handleRateLimitResponse = (error: AxiosError) => {
    if (error.response?.status === 429) {
      const retryAfter = Number(error.response.headers['retry-after']) || 60;
      setState({
        isBlocked: true,
        cooldown: retryAfter,
        retryAfter,
      });
      onRateLimited?.(retryAfter);
      return true;
    }
    return false;
  };

  /**
   * Wrapper for API calls that handles rate limiting
   */
  const executeWithRateLimit = async <T,>(
    apiCall: () => Promise<T>
  ): Promise<T> => {
    try {
      const result = await apiCall();
      // Reset state on successful call
      setState(prev => ({ ...prev, isBlocked: false, cooldown: 0 }));
      return result;
    } catch (error) {
      if (error instanceof AxiosError) {
        const wasRateLimited = handleRateLimitResponse(error);
        if (wasRateLimited) {
          throw new Error(`Rate limited. Please wait ${state.retryAfter || 60} seconds.`);
        }
      }
      throw error;
    }
  };

  return {
    isBlocked: state.isBlocked,
    cooldown: state.cooldown,
    retryAfter: state.retryAfter,
    executeWithRateLimit,
    handleRateLimitResponse,
  };
}

/**
 * Specialized hook for critical operations (cloud instances, accounts, etc.)
 */
export function useCriticalOperationRateLimit(operation: string) {
  return useServerRateLimit({
    operation,
    onRateLimited: (retryAfter) => {
      console.log(`Critical operation "${operation}" rate limited for ${retryAfter} seconds`);
    },
  });
}
