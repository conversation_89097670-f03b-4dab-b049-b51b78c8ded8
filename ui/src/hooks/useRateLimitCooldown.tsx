import { useEffect, useRef, useState } from "react";

export function useRateLimitCooldown(storageKey: string) {
  const [cooldown, setCooldown] = useState(0);
  const timerRef = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    const until = Number(localStorage.getItem(storageKey) || 0);
    const now = Math.floor(Date.now() / 1000);
    if (until > now) {
      setCooldown(until - now);
      startTimer(until - now);
    }
    // eslint-disable-next-line
  }, [storageKey]);

  function startTimer(initial: number) {
    setCooldown(initial);

    if (timerRef.current) clearInterval(timerRef.current);
    timerRef.current = setInterval(() => {
      setCooldown((prev) => {
        if (prev <= 1) {
          if (timerRef.current) clearInterval(timerRef.current);
          localStorage.removeItem(storageKey);
          return 0;
        }
        return prev - 1;
      });
    }, 1000);
  }

  function triggerCooldown(seconds: number) {
    setCooldown(seconds);
    const until = Math.floor(Date.now() / 1000) + seconds;
    localStorage.setItem(storageKey, until.toString());
    startTimer(seconds);
  }

  return {
    cooldown,
    triggerCooldown,
    isBlocked: cooldown > 0,
  };
}
