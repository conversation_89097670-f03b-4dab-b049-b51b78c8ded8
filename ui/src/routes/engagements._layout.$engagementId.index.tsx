import { useSuspenseQuery } from "@tanstack/react-query";
import { Link, createFileRoute, useParams } from "@tanstack/react-router";
import { ChangeEvent, useState, useRef } from "react";
import type { JSX } from "react";
import CloudActionsDropdown from "../components/CloudActionsDropdown";

import {
  getEngagement,
  getEngagementGraphs,
  getGetEngagementCloudInstancesQueryKey,
  getGetEngagementGraphsQueryKey,
  getGetEngagementGraphsQueryOptions,
  getGetEngagementQueryKey,
  getGetEngagementQueryOptions,
  useGetEngagementCloudInstances,
} from "../client.ts";
import { createRefetchCloudInstances } from "../utils/refetchEngagementsStatus";
import EngagementCloudInstancesTable, { EngagementCloudInstancesTableRef } from "../components/EngagementCloudInstancesTable.tsx";
import EngagementInfo from "../components/EngagementInfo.tsx";
import EngagementNodesTable from "../components/EngagementNodesTable.tsx";
import ErrorPage from "../components/ErrorHandling/ErrorPage.tsx";
import NotFound from "../components/ErrorHandling/NotFound.tsx";
import NodeGroupGraph from "../components/GraphView/NodeGroupGraph.tsx";
import { Graph } from "../model/index.ts";
import { errorCode } from "../utils/assets.tsx";

export const Route = createFileRoute("/engagements/_layout/$engagementId/")({
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  errorComponent: ({ error }: any) => {
    const status = error?.status in errorCode ? error?.status : 500;
    const errorData = errorCode[status] || errorCode[500];
    return (
      <ErrorPage
        code={status}
        title={errorData.title}
        description={errorData.description}
        colour={errorData.colour}
      />
    );
  },
  notFoundComponent: () => {
    return <NotFound />;
  },
  component: EngagementComponent,
  loader: async ({ context: { queryClient }, params }) => {
    const getEngagementDetails = queryClient.ensureQueryData(
      getGetEngagementQueryOptions(params.engagementId),
    );

    const getEngagementGraph = queryClient.ensureQueryData(
      getGetEngagementGraphsQueryOptions(params.engagementId),
    );
    const [engagementData, engagementGraph] = await Promise.all([
      getEngagementDetails,
      getEngagementGraph,
    ]);
    return { engagementData, engagementGraph };
  },
});

function EngagementComponent(): JSX.Element {
  const { engagementId }: { engagementId: string } = useParams({
    from: "/engagements/_layout/$engagementId/",
  });
  const engagementQueryKey = getGetEngagementQueryKey(engagementId);
  const queryFn = () => getEngagement(engagementId);
  const { data: engagementList } = useSuspenseQuery({
    queryKey: engagementQueryKey,
    queryFn,
  });
  const engagement = engagementList.engagement || [];

  const graphQueryKey = getGetEngagementGraphsQueryKey(engagementId);
  const graphQueryFn = () => getEngagementGraphs(engagementId);
  const { data: engagementGraph } = useSuspenseQuery({
    queryKey: graphQueryKey,
    queryFn: graphQueryFn,
  });

  const graphs = engagementGraph.graphs || [];

  const [selectedRadio, setSelectedRadio] = useState<string | null>(null);
  const [isResetViewChecked, setResetViewChecked] = useState(false);
  const [isExpanded, setIsExpanded] = useState(false);
  const [selectedNodeGroup, setSelectedNodeGroup] = useState<string | null>(
    null,
  );

  const handleNodeGroupClick = (nodeGroupId: string) => {
    setSelectedNodeGroup((prev) => (prev === nodeGroupId ? "" : nodeGroupId));
    setResetViewChecked(false);
  };

  const handleResetGraphView = () => {
    setSelectedNodeGroup(null);
    setIsExpanded(false);
    setResetViewChecked(true);
    setTimeout(() => setResetViewChecked(false), 0);
  };

  const handleRadioChange = (e: ChangeEvent<HTMLInputElement>) => {
    setSelectedRadio(e.target.value);
  };
  const handleReset = () => {
    setSelectedRadio(null);
  };

  const { data: cloudInstancesList } = useGetEngagementCloudInstances(
    engagementId,
    {
      query: {
        queryKey: getGetEngagementCloudInstancesQueryKey(engagementId),
        enabled: selectedRadio === "cloudRadio",
        refetchInterval: createRefetchCloudInstances({ refetchIntervalMs: 10000, transientStates: ["pending", "stopping", "shutting-down"] }),
      },
    },
  );
  const [selectedCloudNodeIds, setSelectedCloudNodeIds] = useState<string[]>([]);
  const [selectedCloudStates, setSelectedCloudStates] = useState<string[]>([]);
  const cloudInstancesTableRef = useRef<EngagementCloudInstancesTableRef>(null);

  const cloudInstances = cloudInstancesList?.cloud_instance_node_groups || [];

  return (
    <>
      <div
        id="engagements-page-main-title"
        className="flex flex-row justify-between pt-4"
      >
        <div className="text-3xl font-semibold text-black dark:text-white">
          {engagement.title}
        </div>
        <div className="hidden md:flex">
          <Link
            to={`/engagements/$engagementId/user-assignment-logs`}
            params={{ engagementId: engagement.id }}
          >
            <span className="text-indigo-600 underline underline-offset-2 hover:text-indigo-400 dark:text-indigo-300 dark:hover:text-indigo-200">
              User Assignment Logs
            </span>
          </Link>
        </div>
      </div>
      <EngagementInfo engagement={engagement} />
      <div className="mt-6 text-sm font-semibold text-black dark:text-white">
        List of Node Groups
      </div>
      {engagement.node_groups && engagement.node_groups.length > 0 ? (
        <div>
          <div
            id="list-of-node-groups"
            className="relative mt-5 flex min-w-0 flex-col space-y-2"
          >
            <div className="flex w-full min-w-0 flex-col rounded-lg bg-white px-3 py-3 md:px-3 dark:bg-[#374357b5]">
              <div className="flex flex-row justify-between">
                <div className="flex flex-row items-center space-x-5"></div>
                <div className="flex flex-wrap justify-end gap-3 md:mr-2 min-w-0">
                  <label className="dark:text-white">
                    <input
                      type="radio"
                      className="m-2 accent-purple-700"
                      name="instanceType"
                      value="hostRadio"
                      checked={selectedRadio === "hostRadio"}
                      onChange={(e) => handleRadioChange(e)}
                    />
                    Host
                  </label>
                  <label className="dark:text-white inline-flex items-center">
                    <input
                      type="radio"
                      className="m-2 accent-purple-700"
                      value="cloudRadio"
                      checked={selectedRadio === "cloudRadio"}
                      onChange={(e) => handleRadioChange(e)}
                    />
                    Cloud Instance
                  </label>
                  {selectedRadio === "cloudRadio" && (
                    <CloudActionsDropdown
                      engagementId={engagement.id}
                      selectedNodeIds={selectedCloudNodeIds}
                      selectedStates={selectedCloudStates}
                      onActionComplete={() => cloudInstancesTableRef.current?.clearSelection()}
                    />
                  )}
                  <button
                    onClick={handleReset}
                    className="cursor-pointer rounded-sm border border-solid border-gray-400 px-4 py-1 hover:bg-slate-100 dark:text-white dark:hover:bg-slate-600"
                  >
                    Reset
                  </button>
                </div>
              </div>
              {cloudInstances && selectedRadio === "cloudRadio" && (
                <div className="w-full min-w-0 overflow-x-auto">
                  <EngagementCloudInstancesTable
                    ref={cloudInstancesTableRef}
                    nodeGroups={cloudInstances}
                    onSelectionChange={(ids: string[]) => setSelectedCloudNodeIds(ids)}
                    onSelectionStatesChange={(states: string[]) => setSelectedCloudStates(states)}
                    engagementName={engagement.title}
                  />
                </div>
              )}
              {engagement &&
                selectedRadio !== "cloudRadio" &&
                selectedRadio !== "hostRadio" && (
                  <div className="w-full">
                    <EngagementNodesTable
                      nodeGroups={engagement.node_groups}
                      engagementName={engagement.title}
                      engagementID={engagement?.id}
                    />
                  </div>
                )}
            </div>
          </div>
          <div id="node-graph-view" className="flex flex-col space-y-2">
            <div className="mt-9 flex justify-between">
              <span className="text-sm font-semibold text-black dark:text-white">
                Node Graph View
              </span>
              <button
                onClick={handleResetGraphView}
                className="cursor-pointer rounded-sm border border-solid border-gray-400 px-4 py-1 hover:bg-slate-100 dark:text-white dark:hover:bg-slate-600"
              >
                Reset
              </button>
            </div>
            <div className="flex flex-row items-center space-x-4">
              <div className="flex flex-row items-center space-x-1">
                <div className="h-3 w-3 rounded-sm bg-gray-500"></div>
                <span className="text-xs text-black dark:text-white">
                  Not Running
                </span>
              </div>
              <div className="flex flex-row items-center space-x-1">
                <div className="h-3 w-3 rounded-sm bg-blue-200"></div>
                <span className="text-xs text-black dark:text-white">
                  Running
                </span>
              </div>
            </div>
            <div className="mt-2 grid w-full flex-row gap-4 pt-3 md:grid-cols-2">
              {graphs?.map((graph: Graph) => (
                <div
                  key={graph?.node_group_id}
                  id={`graph-${graph?.node_group_id}`}
                  className={`${
                    isResetViewChecked ||
                    selectedNodeGroup === null ||
                    (!isResetViewChecked && selectedNodeGroup === "")
                      ? "w-full"
                      : selectedNodeGroup === graph.node_group_id
                        ? "col-span-2"
                        : "hidden"
                  }`}
                >
                  <NodeGroupGraph
                    graphElements={
                      graph.elements as cytoscape.ElementDefinition[]
                    }
                    nodeGroupID={graph?.node_group_id}
                    nodeGroupName={graph?.node_group_name}
                    onClick={() => handleNodeGroupClick(graph.node_group_id)}
                    isExpanded={isExpanded}
                    setIsExpanded={setIsExpanded}
                    resetViewChecked={isResetViewChecked}
                    engagementID={engagement?.id}
                    nodeGroups={engagement?.node_groups ?? []}
                    engagementName={engagement?.title}
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      ) : (
        <div className="h-screen dark:text-white">-</div>
      )}
    </>
  );
}
