import { Outlet, createFileRoute } from "@tanstack/react-router";
import { useState } from "react";

import BreadCrumbs from "../components/BreadCrumbs";
import Header from "../components/Header";
import ResponsiveSideNav from "../components/ResponsiveSideNav";

export const Route = createFileRoute("/engagements/_layout")({
  component: RouteComponent,
});

function RouteComponent() {
  const [isSideNavOpen, setIsSideNavOpen] = useState(false);
  const toggleSideNav = () => setIsSideNavOpen((prev) => !prev);

  return (
    <div className="flex h-full w-full">
      <ResponsiveSideNav
        toggleSideNav={toggleSideNav}
        isSideNavOpen={isSideNavOpen}
      />
      <div className="flex w-full flex-col">
        <Header toggleSideNav={toggleSideNav} />
        <div className="flex flex-col bg-slate-50 p-6 overflow-x-hidden dark:bg-slate-800">
          <BreadCrumbs />
          <Outlet />
        </div>
      </div>
    </div>
  );
}
