package users

import (
	"context"
	"database/sql"
	"fmt"
	"strings"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	amqp "github.com/rabbitmq/amqp091-go"

	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments/keys"
	"golang.org/x/crypto/ssh"
)

type User struct {
	ID                  string    `json:"id" format:"uuid"`
	FullName            string    `json:"full_name"`
	Username            string    `json:"username"`
	CustomUsername      string    `json:"custom_username"`
	SSHKey              string    `json:"ssh_key"`
	SSHKeyLabel         string    `json:"ssh_key_label"`
	SSHKeyCreationDate  time.Time `json:"ssh_key_creation_date"`
	ValidCustomUsername bool      `json:"valid_custom_username"`
	ValidSshKey         bool      `json:"valid_ssh_key"`
	AppRole             string    `json:"app_role"`
}

type Script struct {
	ID          string            `json:"id" format:"uuid"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Content     string            `json:"content"`
	ScriptType  db.ScriptTypeEnum `json:"script_type" enum:"STANDARD,ADMIN"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	UserID      string            `json:"user_id"`
}

func GetUsers(queries *db.Queries) ([]User, error) {
	usersDB, err := queries.GetUsers(context.Background())
	if err != nil {
		return nil, err
	}

	var users []User

	for _, userDB := range usersDB {
		userIDString, err := converters.PgTypeUUIDToString(userDB.ID)
		if err != nil {
			return nil, err
		}

		_, _, _, _, err = ssh.ParseAuthorizedKey([]byte(userDB.SshKey.String))
		isValidSshKey := true
		if err != nil {
			isValidSshKey = false
		}

		user := User{
			ID:                  *userIDString,
			FullName:            userDB.FullName.String,
			Username:            userDB.Username,
			CustomUsername:      userDB.CustomUsername.String,
			AppRole:             userDB.AppRole.String,
			SSHKey:              userDB.SshKey.String,
			SSHKeyLabel:         userDB.SshKeyLabel.String,
			SSHKeyCreationDate:  userDB.SshKeyCreationDate.Time,
			ValidCustomUsername: len(userDB.CustomUsername.String) > 0 && userDB.CustomUsername.Valid,
			ValidSshKey:         isValidSshKey,
		}
		users = append(users, user)
	}
	return users, nil
}

func GetUserForUserSettings(queries *db.Queries, userID string) (*User, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	userDB, err := queries.GetUserForUserSettings(context.Background(), *userIDPgType)
	if err != nil {
		return nil, err
	}

	user := User{
		FullName:           userDB.FullName.String,
		Username:           userDB.Username,
		CustomUsername:     userDB.CustomUsername.String,
		SSHKey:             userDB.SshKey.String,
		SSHKeyLabel:        userDB.SshKeyLabel.String,
		SSHKeyCreationDate: userDB.SshKeyCreationDate.Time,
	}

	return &user, nil
}

func UpdateUserUsername(queries *db.Queries, ch *amqp.Channel, custom_username pgtype.Text, userID string, awsRootRegion string, secretKey string) error {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return err
	}

	// Get the old username before updating
	oldUser, err := queries.GetUserForUserSettings(context.Background(), *userIDPgType)
	if err != nil {
		return fmt.Errorf("failed to get current user data: %w", err)
	}
	oldUsername := oldUser.CustomUsername.String

	existingUser, err := queries.GetUsersWithUsername(context.Background(), custom_username)
	if err != nil && err != sql.ErrNoRows {
		return err
	}

	if existingUser > 0 {
		return fmt.Errorf("username '%s' is already taken", custom_username.String)
	}

	// Update username in database
	err = queries.UpdateUserUsername(context.Background(), db.UpdateUserUsernameParams{
		CustomUsername: custom_username,
		ID:             *userIDPgType,
	})
	if err != nil {
		return err
	}

	// Track deleted username in history and handle OS-level access changes
	fmt.Printf("USERNAME_UPDATE: user=%s old='%s' new='%s' hasSSHKey=%v\n", userID, oldUsername, custom_username.String, oldUser.SshKey.Valid && oldUser.SshKey.String != "")

	if oldUsername != "" && oldUsername != custom_username.String {
		if custom_username.Valid && custom_username.String != "" {
			// Username changed to a new non-empty value: record old as deleted and handle both revoke + recreate
			fmt.Printf("USERNAME_UPDATE: user=%s changing from '%s' to '%s' - will revoke old and recreate new\n", userID, oldUsername, custom_username.String)

			// Handle both revoke and recreate in a single goroutine to avoid race conditions
			// if oldUser.SshKey.Valid && oldUser.SshKey.String != "" {
			// 	go func(uid, old, newU string) {
			// 		// First: Revoke access for old username
			// 		fmt.Printf("USERNAME_UPDATE_REVOKE: Starting revoke for user=%s oldUsername='%s'\n", uid, old)
			// 		if err := RevokeUserSshAccessForUsernameOnInstances(queries, uid, old, awsRootRegion, secretKey); err != nil {
			// 			fmt.Printf("USERNAME_UPDATE_REVOKE: FAILED for user=%s oldUsername='%s': %v\n", uid, old, err)
			// 		} else {
			// 			fmt.Printf("USERNAME_UPDATE_REVOKE: COMPLETED for user=%s oldUsername='%s'\n", uid, old)
			// 		}

			// 		// Second: Recreate directories with new username (this will also remove old keys as a safety measure)
			// 		fmt.Printf("USERNAME_UPDATE_RECREATE: Starting recreate for user=%s old='%s' new='%s'\n", uid, old, newU)
			// 		if err := RecreateUserDirectoriesOnInstances(queries, uid, old, newU, awsRootRegion, secretKey); err != nil {
			// 			fmt.Printf("USERNAME_UPDATE_RECREATE: FAILED for user=%s old='%s' new='%s': %v\n", uid, old, newU, err)
			// 		} else {
			// 			fmt.Printf("USERNAME_UPDATE_RECREATE: COMPLETED for user=%s old='%s' new='%s'\n", uid, old, newU)
			// 		}
			// 	}(userID, oldUsername, custom_username.String)
			// Enqueue revoke old username
			// ch, _ := rabbitMqManager.GetChannel()
			// Queue background propagation via worker
			if oldUser.SshKey.Valid && oldUser.SshKey.String != "" {
				if _, err := QueueUsernameDeleteOperation(ch, queries, userID, oldUsername, custom_username.String); err != nil {
					return fmt.Errorf("failed to queue USERNAME_DELETE(+create): %w", err)
				}
			} else {
				if _, err := QueueUsernameDeleteOperation(ch, queries, userID, oldUsername, ""); err != nil {
					return fmt.Errorf("failed to queue USERNAME_DELETE (no key): %w", err)
				}
			}
		} else {
			// Username was cleared (deleted): queue revoke access for old username
			fmt.Printf("USERNAME_UPDATE: user=%s clearing username '%s' - will revoke access\n", userID, oldUsername)
			if _, err := QueueUsernameDeleteOperation(ch, queries, userID, oldUsername, ""); err != nil {
				return fmt.Errorf("failed to queue USERNAME_DELETE (cleared): %w", err)
			}
		}

	} else {
		// No username change, but if user has SSH key and new username, just recreate directories
		if oldUser.SshKey.Valid && oldUser.SshKey.String != "" && custom_username.Valid && custom_username.String != "" {
			fmt.Printf("USERNAME_UPDATE: user=%s no username change, queuing recreate directories for '%s'\n", userID, custom_username.String)
			if err := QueueSSHKeyOperation(ch, queries, userID, "SSH_KEY_ADD", "", ""); err != nil {
				return fmt.Errorf("failed to queue SSH_KEY_ADD (recreate-only): %w", err)
			}
		}
	}

	return nil
}

func GetScripts(queries *db.Queries, userID string) ([]Script, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	scriptsDB, err := queries.GetScriptsByUser(context.Background(), *userIDPgType)
	if err != nil {
		return nil, err
	}

	var scripts []Script
	for _, scriptDB := range scriptsDB {
		scriptIDString, _ := converters.PgTypeUUIDToString(scriptDB.ID)
		userIDString, _ := converters.PgTypeUUIDToString(scriptDB.UserID)
		script := Script{
			ID:          *scriptIDString,
			Name:        scriptDB.Name,
			Description: scriptDB.Description,
			Content:     scriptDB.Content,
			ScriptType:  scriptDB.ScriptType,
			CreatedAt:   scriptDB.CreatedAt.Time,
			UpdatedAt:   scriptDB.UpdatedAt.Time,
			UserID:      *userIDString,
		}
		scripts = append(scripts, script)
	}
	return scripts, nil
}

func CreateNewScript(queries *db.Queries, name string, description string, content string, scriptType db.ScriptTypeEnum, userID string) (*Script, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	newScript, err := queries.CreateScript(context.Background(), db.CreateScriptParams{
		Name:        name,
		Description: description,
		Content:     content,
		UserID:      *userIDPgType,
		CreatedAt: pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		},
	})
	if err != nil {
		return nil, err
	}

	scriptIDString, err := converters.PgTypeUUIDToString(newScript.ID)
	if err != nil {
		return nil, err
	}

	userIDString, _ := converters.PgTypeUUIDToString(newScript.UserID)
	if err != nil {
		return nil, err
	}

	scriptResponse := Script{
		ID:          *scriptIDString,
		Name:        newScript.Name,
		Description: newScript.Description,
		Content:     newScript.Content,
		ScriptType:  scriptType,
		CreatedAt:   newScript.CreatedAt.Time,
		UpdatedAt:   newScript.UpdatedAt.Time,
		UserID:      *userIDString,
	}
	return &scriptResponse, nil
}

func UpdateScript(queries *db.Queries, scriptID string, name string, description string, content string, scriptType db.ScriptTypeEnum, userID string) (*Script, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	scriptIDPgType, err := converters.StringToPgTypeUUID(scriptID)
	if err != nil {
		return nil, err
	}

	updatedScript, err := queries.EditScript(context.Background(), db.EditScriptParams{
		ID:          *scriptIDPgType,
		Name:        name,
		Description: description,
		Content:     content,
		UserID:      *userIDPgType,
	})
	if err != nil {
		return nil, err
	}

	scriptResponse := Script{
		ID:          scriptID,
		Name:        updatedScript.Name,
		Description: updatedScript.Description,
		Content:     updatedScript.Content,
		ScriptType:  scriptType,
		CreatedAt:   updatedScript.CreatedAt.Time,
		UpdatedAt:   updatedScript.UpdatedAt.Time,
		UserID:      userID,
	}

	return &scriptResponse, nil
}

func DeleteScript(queries *db.Queries, scriptID string, userID string) error {
	scriptIDPgType, err := converters.StringToPgTypeUUID(scriptID)
	if err != nil {
		return err
	}

	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return err
	}

	err = queries.DeleteScript(context.Background(), db.DeleteScriptParams{
		ID:     *scriptIDPgType,
		UserID: *userIDPgType,
	})
	if err != nil {
		return err
	}

	return nil
}

// RecreateUserDirectoriesOnInstances recreates user directories on all cloud instances
// where the user is assigned when their username changes
func RecreateUserDirectoriesOnInstances(queries *db.Queries, userID string, oldUsername string, newUsername string, awsRootRegion string, secretKey string) error {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return fmt.Errorf("failed to convert user ID: %w", err)
	}

	// Get user's SSH key
	userSSHKey, err := queries.GetUserSshKey(context.Background(), *userIDPgType)
	if err != nil {
		return fmt.Errorf("failed to get user SSH key: %w", err)
	}

	if !userSSHKey.Valid || userSSHKey.String == "" {
		return fmt.Errorf("user has no valid SSH key")
	}

	// Get all engagements the user belongs to
	userEngagements, err := queries.GetUserEngagements(context.Background(), *userIDPgType)
	if err != nil {
		return fmt.Errorf("failed to get user engagements: %w", err)
	}

	fmt.Printf("RECREATE_DIRS: user=%s found %d engagements\n", userID, len(userEngagements))

	// For each engagement, get cloud instances and recreate user directories
	for _, engagement := range userEngagements {
		engagementIDStr, _ := converters.PgTypeUUIDToString(engagement.ID)
		fmt.Printf("RECREATE_DIRS: user=%s processing engagement=%s\n", userID, *engagementIDStr)
		err = recreateUserDirectoriesForEngagement(queries, engagement.ID, oldUsername, newUsername, userSSHKey.String, awsRootRegion, secretKey)
		if err != nil {
			// Log error but continue with other engagements
			fmt.Printf("RECREATE_DIRS: FAILED for user=%s engagement=%s: %v\n", userID, *engagementIDStr, err)
		} else {
			fmt.Printf("RECREATE_DIRS: COMPLETED for user=%s engagement=%s\n", userID, *engagementIDStr)
		}
	}

	return nil
}

// recreateUserDirectoriesForEngagement handles recreating user directories for a specific engagement
func recreateUserDirectoriesForEngagement(queries *db.Queries, engagementID pgtype.UUID, oldUsername string, newUsername string, userSSHKey string, awsRootRegion string, secretKey string) error {
	// Log engagement context for correlation
	engagementIDStr, _ := converters.PgTypeUUIDToString(engagementID)
	fmt.Printf("RecreateUserDirs: engagement=%s old=%s new=%s\n", *engagementIDStr, oldUsername, newUsername)

	// Get all cloud instances for this engagement
	cloudInstances, err := queries.GetEngagementCloudInstances(context.Background(), engagementID)
	if err != nil {
		return fmt.Errorf("failed to get cloud instances for engagement: %w", err)
	}

	fmt.Printf("RECREATE_DIRS_ENG: engagement=%s found %d instances\n", *engagementIDStr, len(cloudInstances))

	if len(cloudInstances) == 0 {
		// No instances to update, this is fine
		fmt.Printf("RECREATE_DIRS_ENG: engagement=%s has no instances, skipping\n", *engagementIDStr)
		return nil
	}

	// Initialize secrets manager and per-account cache (like in RevokeUserSshAccessForUsernameOnInstances)
	sm, err := keys.NewSecretsManager(awsRootRegion)
	if err != nil {
		return fmt.Errorf("failed to initialize secrets manager: %w", err)
	}

	type acctCred struct{ key, pass, adminUser string }
	cache := map[string]acctCred{}

	// Process each cloud instance (get credentials per account like in RevokeUserSshAccessForUsernameOnInstances)
	for _, cloudInstance := range cloudInstances {
		nodeIDStr, _ := converters.PgTypeUUIDToString(cloudInstance.NodeID)
		instanceIP := "unknown"
		if cloudInstance.PublicIpv4Address != nil {
			instanceIP = strings.Replace(cloudInstance.PublicIpv4Address.String(), "\"", "", -1)
		}

		// Skip instances without public IP addresses
		if cloudInstance.PublicIpv4Address == nil {
			fmt.Printf("RECREATE_DIRS_INST: engagement=%s skipping instance node_id=%s (no public IP)\n", *engagementIDStr, *nodeIDStr)
			continue
		}

		// Only process running instances (same as RevokeUserSshAccessForUsernameOnInstances)
		if !cloudInstance.CloudInstanceState.Valid || cloudInstance.CloudInstanceState.CiStateEnum != "running" {
			state := "unknown"
			if cloudInstance.CloudInstanceState.Valid {
				state = string(cloudInstance.CloudInstanceState.CiStateEnum)
			}
			fmt.Printf("RECREATE_DIRS_INST: engagement=%s instance node_id=%s ip=%s - not running (state: %s), skipping\n", *engagementIDStr, *nodeIDStr, instanceIP, state)
			continue
		}

		fmt.Printf("RECREATE_DIRS_INST: engagement=%s processing instance node_id=%s ip=%s\n", *engagementIDStr, *nodeIDStr, instanceIP)

		// Get account credentials for this specific instance
		var accountUUID pgtype.UUID
		var isAzure bool
		if cloudInstance.AwsAccountID.Valid {
			accountUUID = cloudInstance.AwsAccountID
			isAzure = false
		} else if cloudInstance.AzureTenantID.Valid {
			accountUUID = cloudInstance.AzureTenantID
			isAzure = true
		} else {
			fmt.Printf("RECREATE_DIRS_INST: engagement=%s instance node_id=%s - no account ID, skipping\n", *engagementIDStr, *nodeIDStr)
			continue
		}

		accountIDStr, _ := converters.PgTypeUUIDToString(accountUUID)
		cred, ok := cache[*accountIDStr]
		if !ok {
			fmt.Printf("RECREATE_DIRS_INST: engagement=%s instance node_id=%s account=%s - fetching credentials (Azure: %v)\n", *engagementIDStr, *nodeIDStr, *accountIDStr, isAzure)
			secretValues, err := sm.GetSecret(*accountIDStr)
			if err != nil {
				fmt.Printf("RECREATE_DIRS_INST: engagement=%s instance node_id=%s account=%s - GetSecret failed: %v\n", *engagementIDStr, *nodeIDStr, *accountIDStr, err)
				continue
			}
			sshPrivateKey, ok2 := secretValues["ssh_private_key"]
			if !ok2 {
				fmt.Printf("RECREATE_DIRS_INST: engagement=%s instance node_id=%s account=%s - ssh_private_key missing\n", *engagementIDStr, *nodeIDStr, *accountIDStr)
				continue
			}

			// Use the correct query based on provider type
			var policyID, statusID string
			if isAzure {
				azureKey, err := queries.GetAzureTenantSshPrivateKey(context.Background(), accountUUID)
				if err != nil {
					fmt.Printf("RECREATE_DIRS_INST: engagement=%s instance node_id=%s account=%s - GetAzureTenantSshPrivateKey failed: %v\n", *engagementIDStr, *nodeIDStr, *accountIDStr, err)
					continue
				}
				policyID = azureKey.PolicyID
				statusID = azureKey.StatusID
			} else {
				awsKey, err := queries.GetAccountSshPrivateKey(context.Background(), accountUUID)
				if err != nil {
					fmt.Printf("RECREATE_DIRS_INST: engagement=%s instance node_id=%s account=%s - GetAccountSshPrivateKey failed: %v\n", *engagementIDStr, *nodeIDStr, *accountIDStr, err)
					continue
				}
				policyID = awsKey.PolicyID
				statusID = awsKey.StatusID
			}
			decryptedSecret, err := keys.DecryptSecret(policyID, statusID, secretKey)
			if err != nil {
				fmt.Printf("RECREATE_DIRS_INST: engagement=%s instance node_id=%s account=%s - DecryptSecret failed: %v\n", *engagementIDStr, *nodeIDStr, *accountIDStr, err)
				continue
			}
			adminUserFromSecret := ""
			if au, ok := secretValues["admin_username"]; ok {
				adminUserFromSecret = au
			}
			cred = acctCred{key: sshPrivateKey, pass: string(decryptedSecret), adminUser: adminUserFromSecret}
			cache[*accountIDStr] = cred
			fmt.Printf("RECREATE_DIRS_INST: engagement=%s instance node_id=%s account=%s - credentials cached\n", *engagementIDStr, *nodeIDStr, *accountIDStr)
		}

		err = recreateUserDirectoryOnInstanceWithCreds(cloudInstance, cred.key, cred.pass, cred.adminUser, oldUsername, newUsername, userSSHKey, *engagementIDStr)
		if err != nil {
			fmt.Printf("RECREATE_DIRS_INST: FAILED engagement=%s instance node_id=%s ip=%s: %v\n", *engagementIDStr, *nodeIDStr, instanceIP, err)
			// Continue with other instances
		} else {
			fmt.Printf("RECREATE_DIRS_INST: COMPLETED engagement=%s instance node_id=%s ip=%s\n", *engagementIDStr, *nodeIDStr, instanceIP)
		}
	}

	return nil
}

// recreateUserDirectoryOnInstanceWithCreds handles the SSH operations to recreate a user directory on a single instance
// Uses the same admin user fallback pattern as RevokeUserSshAccessForUsernameOnInstances
func recreateUserDirectoryOnInstanceWithCreds(cloudInstance db.GetEngagementCloudInstancesRow, sshPrivateKey string, passphrase string, adminUserFromSecret string, oldUsername string, newUsername string, userSSHKey string, engagementID string) error {
	nodeIDStr, _ := converters.PgTypeUUIDToString(cloudInstance.NodeID)
	instanceIP := strings.Replace(cloudInstance.PublicIpv4Address.String(), "\"", "", -1)

	fmt.Printf("RECREATE_INST: engagement=%s node_id=%s ip=%s old='%s' new='%s' - starting\n", engagementID, *nodeIDStr, instanceIP, oldUsername, newUsername)

	if cloudInstance.PublicIpv4Address == nil {
		return fmt.Errorf("instance has no public IP address")
	}

	// Optimized primary user + fallback approach for username recreation
	address := fmt.Sprintf("%s:22", instanceIP)

	// Determine provider type
	var isAzure bool
	if cloudInstance.AwsAccountID.Valid {
		isAzure = false
	} else if cloudInstance.AzureTenantID.Valid {
		isAzure = true
	}

	var primaryUser string
	var fallbackUsers []string

	if isAzure {
		primaryUser = "azureuser"
		fallbackUsers = []string{"admin", "ubuntu"}
	} else {
		primaryUser = "admin"
		fallbackUsers = []string{"ubuntu", "ec2-user"}
	}

	fmt.Printf("RECREATE_INST: engagement=%s node_id=%s ip=%s - trying primary SSH user '%s'\n", engagementID, *nodeIDStr, instanceIP, primaryUser)
	var sshClient *keys.SSHClient

	// Try primary user with more retries (higher chance of success)
	tmp := keys.NewSSHClient(address, primaryUser, sshPrivateKey, passphrase, 5, 5*time.Second)
	if err := tmp.Connect(); err == nil {
		sshClient = tmp
		fmt.Printf("RECREATE_INST: engagement=%s node_id=%s ip=%s - SSH connection SUCCESS with primary user '%s'\n", engagementID, *nodeIDStr, instanceIP, primaryUser)
	} else {
		fmt.Printf("RECREATE_INST: engagement=%s node_id=%s ip=%s - primary user '%s' failed: %v, trying fallbacks\n", engagementID, *nodeIDStr, instanceIP, primaryUser, err)

		// Try fallback users with fewer retries
		for _, fallbackUser := range fallbackUsers {
			fmt.Printf("RECREATE_INST: engagement=%s node_id=%s ip=%s - trying fallback SSH user '%s'\n", engagementID, *nodeIDStr, instanceIP, fallbackUser)
			tmp := keys.NewSSHClient(address, fallbackUser, sshPrivateKey, passphrase, 2, 5*time.Second)
			if err := tmp.Connect(); err == nil {
				sshClient = tmp
				fmt.Printf("RECREATE_INST: engagement=%s node_id=%s ip=%s - SSH connection SUCCESS with fallback user '%s'\n", engagementID, *nodeIDStr, instanceIP, fallbackUser)
				break
			} else {
				fmt.Printf("RECREATE_INST: engagement=%s node_id=%s ip=%s - fallback user '%s' failed: %v\n", engagementID, *nodeIDStr, instanceIP, fallbackUser, err)
			}
		}
	}
	if sshClient == nil {
		fmt.Printf("RECREATE_INST: engagement=%s node_id=%s ip=%s - no SSH connection possible, skipping\n", engagementID, *nodeIDStr, instanceIP)
		return fmt.Errorf("failed to connect to instance %s with any admin user", instanceIP)
	}
	defer sshClient.Close()

	// If there's an old username, revoke its SSH access while preserving the home directory
	if oldUsername != "" && oldUsername != newUsername {
		fmt.Printf("RECREATE_INST: engagement=%s node_id=%s ip=%s - revoking SSH access for old username '%s'\n", engagementID, *nodeIDStr, instanceIP, oldUsername)
		if err := sshClient.RemoveUserSshKey(oldUsername); err != nil {
			// Log but continue; we still want to create the new user
			fmt.Printf("RECREATE_INST: engagement=%s node_id=%s ip=%s - FAILED to revoke SSH key for old username '%s': %v\n", engagementID, *nodeIDStr, instanceIP, oldUsername, err)
		} else {
			fmt.Printf("RECREATE_INST: engagement=%s node_id=%s ip=%s - SUCCESS revoked SSH access for old username '%s' (home preserved)\n", engagementID, *nodeIDStr, instanceIP, oldUsername)
		}
	}

	// Create the new user with the new username and the user's personal SSH key
	fmt.Printf("RECREATE_INST: engagement=%s node_id=%s ip=%s - creating new user '%s' with personal SSH key\n", engagementID, *nodeIDStr, instanceIP, newUsername)
	err := sshClient.AddUser(newUsername, userSSHKey, "fusionx")
	if err != nil {
		fmt.Printf("RECREATE_INST: engagement=%s node_id=%s ip=%s - FAILED to add user '%s': %v\n", engagementID, *nodeIDStr, instanceIP, newUsername, err)
		return fmt.Errorf("failed to add user %s to instance %s: %w", newUsername, instanceIP, err)
	}

	fmt.Printf("RECREATE_INST: engagement=%s node_id=%s ip=%s - SUCCESS created user '%s' with personal SSH key\n", engagementID, *nodeIDStr, instanceIP, newUsername)
	return nil
}
