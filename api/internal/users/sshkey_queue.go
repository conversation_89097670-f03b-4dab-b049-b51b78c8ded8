package users

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

const SSHKeyQueueName = "ssh-key-operations-queue"

type SSHKeyOperationMessage struct {
	OperationID   string    `json:"operation_id"`
	UserID        string    `json:"user_id"`
	OperationType string    `json:"operation_type"` // "SSH_KEY_DELETE", "SSH_KEY_ADD", "USERNAME_DELETE"
	SSHKey        string    `json:"ssh_key,omitempty"`
	SSHKeyLabel   string    `json:"ssh_key_label,omitempty"`
	OldUsername   string    `json:"old_username,omitempty"`
	NewUsername   string    `json:"new_username,omitempty"`
	Timestamp     time.Time `json:"timestamp"`
}

// QueueSSHKeyOperation queues an SSH key operation for background processing
func QueueSSHKeyOperation(ch *amqp.Channel, queries *db.Queries, userID, operationType, sshKey, sshKeyLabel string) error {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	// Convert operation type to ENUM
	var opTypeEnum db.SshKeyOperationTypeEnum
	switch operationType {
	case "SSH_KEY_DELETE":
		opTypeEnum = db.SshKeyOperationTypeEnumSSHKEYDELETE
	case "SSH_KEY_ADD":
		opTypeEnum = db.SshKeyOperationTypeEnumSSHKEYADD
	case "USERNAME_DELETE":
		opTypeEnum = db.SshKeyOperationTypeEnumUSERNAMEDELETE
	case "USERNAME_CREATE":
		opTypeEnum = db.SshKeyOperationTypeEnumUSERNAMECREATE
	default:
		return fmt.Errorf("invalid operation type: %s", operationType)
	}

	// Insert operation record in database
	operationID, err := queries.InsertSSHKeyOperation(context.Background(), db.InsertSSHKeyOperationParams{
		UserID:        *userIDPgType,
		OperationType: db.NullSshKeyOperationTypeEnum{SshKeyOperationTypeEnum: opTypeEnum, Valid: true},
		SshKey:        pgtype.Text{String: sshKey, Valid: sshKey != ""},
		SshKeyLabel:   pgtype.Text{String: sshKeyLabel, Valid: sshKeyLabel != ""},
	})
	if err != nil {
		return fmt.Errorf("failed to insert SSH key operation: %w", err)
	}

	operationIDStr, err := converters.PgTypeUUIDToString(operationID)
	if err != nil {
		return fmt.Errorf("failed to convert operation ID: %w", err)
	}

	// Create message
	message := SSHKeyOperationMessage{
		OperationID:   *operationIDStr,
		UserID:        userID,
		OperationType: operationType,
		SSHKey:        sshKey,
		SSHKeyLabel:   sshKeyLabel,
		Timestamp:     time.Now(),
	}

	body, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal SSH key operation message: %w", err)
	}

	// Publish to queue
	err = ch.PublishWithContext(context.Background(),
		"",              // exchange
		SSHKeyQueueName, // routing key
		false,           // mandatory
		false,           // immediate
		amqp.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp.Persistent, // Make message persistent
		},
	)
	if err != nil {
		return fmt.Errorf("failed to publish SSH key operation message: %w", err)
	}

	fmt.Printf("SSH_KEY_QUEUE: Queued operation %s for user %s (operation_id: %s)\n",
		operationType, userID, *operationIDStr)
	return nil
}

// QueueUsernameDeleteOperation queues a USERNAME_DELETE operation for the old username.
// If newUsername is non-empty, the worker will also create the new user directories
// within the same operation (single row), so the UI sees one loader covering both steps.
func QueueUsernameDeleteOperation(ch *amqp.Channel, queries *db.Queries, userID, oldUsername, newUsername string) (string, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return "", fmt.Errorf("invalid user ID: %w", err)
	}

	// Insert single operation record in database
	opTypeEnum := db.SshKeyOperationTypeEnumUSERNAMEDELETE
	operationID, err := queries.InsertSSHKeyOperation(context.Background(), db.InsertSSHKeyOperationParams{
		UserID:        *userIDPgType,
		OperationType: db.NullSshKeyOperationTypeEnum{SshKeyOperationTypeEnum: opTypeEnum, Valid: true},
		SshKey:        pgtype.Text{Valid: false},
		SshKeyLabel:   pgtype.Text{Valid: false},
	})
	if err != nil {
		return "", fmt.Errorf("failed to insert USERNAME_DELETE operation: %w", err)
	}

	// Store the old username on this operation row for auditing/visibility
	if setErr := queries.SetOperationUsername(context.Background(), operationID, oldUsername); setErr != nil {
		fmt.Printf("Warning: failed to set operation username for %v: %v\n", operationID, setErr)
	}

	operationIDStr, err := converters.PgTypeUUIDToString(operationID)
	if err != nil {
		return "", fmt.Errorf("failed to convert operation ID: %w", err)
	}

	// Create message with old (and optionally new) username context
	message := SSHKeyOperationMessage{
		OperationID:   *operationIDStr,
		UserID:        userID,
		OperationType: "USERNAME_DELETE",
		OldUsername:   oldUsername,
		NewUsername:   newUsername,
		Timestamp:     time.Now(),
	}

	body, err := json.Marshal(message)
	if err != nil {
		return "", fmt.Errorf("failed to marshal USERNAME_DELETE message: %w", err)
	}

	// Publish to queue
	if err := ch.PublishWithContext(context.Background(),
		"",              // exchange
		SSHKeyQueueName, // routing key
		false,           // mandatory
		false,           // immediate
		amqp.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp.Persistent,
		},
	); err != nil {
		return "", fmt.Errorf("failed to publish USERNAME_DELETE message: %w", err)
	}

	fmt.Printf("SSH_KEY_QUEUE: Queued USERNAME_DELETE for user %s (operation_id: %s, old_username: %s, new_username: %s)\n",
		userID, *operationIDStr, oldUsername, newUsername)
	return *operationIDStr, nil
}

// QueueUsernameCreateOperation queues a USERNAME_CREATE operation with old/new username context
func QueueUsernameCreateOperation(ch *amqp.Channel, queries *db.Queries, userID, oldUsername, newUsername string) error {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return fmt.Errorf("invalid user ID: %w", err)
	}

	opTypeEnum := db.SshKeyOperationTypeEnumUSERNAMECREATE
	operationID, err := queries.InsertSSHKeyOperation(context.Background(), db.InsertSSHKeyOperationParams{
		UserID:        *userIDPgType,
		OperationType: db.NullSshKeyOperationTypeEnum{SshKeyOperationTypeEnum: opTypeEnum, Valid: true},
		SshKey:        pgtype.Text{Valid: false},
		SshKeyLabel:   pgtype.Text{Valid: false},
	})
	if err != nil {
		return fmt.Errorf("failed to insert USERNAME_CREATE operation: %w", err)
	}

	operationIDStr, err := converters.PgTypeUUIDToString(operationID)
	if err != nil {
		return fmt.Errorf("failed to convert operation ID: %w", err)
	}

	message := SSHKeyOperationMessage{
		OperationID:   *operationIDStr,
		UserID:        userID,
		OperationType: "USERNAME_CREATE",
		OldUsername:   oldUsername,
		NewUsername:   newUsername,
		Timestamp:     time.Now(),
	}

	body, err := json.Marshal(message)
	if err != nil {
		return fmt.Errorf("failed to marshal USERNAME_CREATE message: %w", err)
	}

	err = ch.PublishWithContext(context.Background(),
		"",              // exchange
		SSHKeyQueueName, // routing key
		false,           // mandatory
		false,           // immediate
		amqp.Publishing{
			ContentType:  "application/json",
			Body:         body,
			DeliveryMode: amqp.Persistent,
		},
	)
	if err != nil {
		return fmt.Errorf("failed to publish USERNAME_CREATE message: %w", err)
	}

	fmt.Printf("SSH_KEY_QUEUE: Queued USERNAME_CREATE for user %s (operation_id: %s, old_username: %s, new_username: %s)\n",
		userID, *operationIDStr, oldUsername, newUsername)
	return nil
}

// StartSSHKeyOperationWorker starts the SSH key operation worker
func StartSSHKeyOperationWorker(ch *amqp.Channel, queries *db.Queries, logger *slog.Logger, awsRootRegion, secretKey string) error {
	// Declare queue
	q, err := ch.QueueDeclare(
		SSHKeyQueueName, // name
		true,            // durable
		false,           // delete when unused
		false,           // exclusive
		false,           // no-wait
		nil,             // arguments
	)
	if err != nil {
		return fmt.Errorf("failed to declare SSH key operations queue: %w", err)
	}

	// Set QoS to process one message at a time per worker
	err = ch.Qos(1, 0, false)
	if err != nil {
		return fmt.Errorf("failed to set QoS: %w", err)
	}

	msgs, err := ch.Consume(
		q.Name, // queue
		"",     // consumer
		false,  // auto-ack (we'll manually ack after processing)
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	if err != nil {
		return fmt.Errorf("failed to register consumer: %w", err)
	}

	logger.Info("SSH key operation worker started", "queue", q.Name)

	// Start worker goroutine
	go func() {
		for d := range msgs {
			var message SSHKeyOperationMessage
			if err := json.Unmarshal(d.Body, &message); err != nil {
				logger.Error("Failed to unmarshal SSH key operation message", "error", err)
				if err := d.Nack(false, false); err != nil {
					logger.Error("Failed to Nack malformed message", "error", err)
				}
				continue
			}

			logger.Info("Processing SSH key operation",
				"operation_id", message.OperationID,
				"user_id", message.UserID,
				"operation_type", message.OperationType)

			// Process the operation
			err := processSSHKeyOperation(queries, message, awsRootRegion, secretKey)
			if err != nil {
				logger.Error("SSH key operation failed",
					"operation_id", message.OperationID,
					"error", err)

				// Update operation status to failed
				// Update DB status to FAILED
				ctx := context.Background()
				opID, convErr := converters.StringToPgTypeUUID(message.OperationID)
				if convErr != nil {
					logger.Error("Invalid operation ID for FAILED status update", "operation_id", message.OperationID, "error", convErr)
				} else {
					params := db.UpdateSSHKeyOperationStatusParams{
						ID:           *opID,
						Column2:      db.SshKeyOperationStatusEnumFAILED,
						ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
						RetryCount:   pgtype.Int4{Int32: 0, Valid: true}, // Could implement retry logic here
					}
					if updateErr := queries.UpdateSSHKeyOperationStatus(ctx, params); updateErr != nil {
						logger.Error("Failed to update operation status to FAILED", "operation_id", message.OperationID, "error", updateErr)
					}
				}

				if err3 := d.Nack(false, false); err3 != nil {
					logger.Error("Failed to Nack message after failure", "operation_id", message.OperationID, "error", err3)
				}
			} else {
				logger.Info("SSH key operation completed successfully",
					"operation_id", message.OperationID)

				// Update operation status to completed
				// Update DB status to COMPLETED
				ctx := context.Background()
				opID, convErr := converters.StringToPgTypeUUID(message.OperationID)
				if convErr != nil {
					logger.Error("Invalid operation ID for COMPLETED status update", "operation_id", message.OperationID, "error", convErr)
				} else {
					params := db.UpdateSSHKeyOperationStatusParams{
						ID:           *opID,
						Column2:      db.SshKeyOperationStatusEnumCOMPLETED,
						ErrorMessage: pgtype.Text{Valid: false},
						RetryCount:   pgtype.Int4{Int32: 0, Valid: true},
					}
					if updateErr := queries.UpdateSSHKeyOperationStatus(ctx, params); updateErr != nil {
						logger.Error("Failed to update operation status to COMPLETED", "operation_id", message.OperationID, "error", updateErr)
					}
				}

				if err := d.Ack(false); err != nil {
					logger.Error("Failed to Ack message after success", "operation_id", message.OperationID, "error", err)
				}
			}
		}
	}()

	return nil
}

// processSSHKeyOperation processes a single SSH key operation
func processSSHKeyOperation(queries *db.Queries, message SSHKeyOperationMessage, awsRootRegion, secretKey string) error {
	// Mark operation as in progress
	operationIDPgType, err := converters.StringToPgTypeUUID(message.OperationID)
	if err != nil {
		return fmt.Errorf("invalid operation ID: %w", err)
	}

	err = queries.UpdateSSHKeyOperationStatus(context.Background(), db.UpdateSSHKeyOperationStatusParams{
		ID:           *operationIDPgType,
		Column2:      db.SshKeyOperationStatusEnumINPROGRESS,
		ErrorMessage: pgtype.Text{Valid: false},
		RetryCount:   pgtype.Int4{Int32: 0, Valid: true},
	})
	if err != nil {
		return fmt.Errorf("failed to update operation status: %w", err)
	}

	// Process based on operation type
	switch message.OperationType {
	case "SSH_KEY_DELETE":
		return PropagateUserSshKeyRemovalToInstances(queries, message.UserID, awsRootRegion, secretKey)
	case "SSH_KEY_ADD":
		return PropagateUserSshKeyAdditionToInstances(queries, message.UserID, awsRootRegion, secretKey)
	case "USERNAME_DELETE":
		// First revoke access for the old username
		if err := RevokeUserSshAccessForUsernameOnInstances(queries, message.UserID, message.OldUsername, awsRootRegion, secretKey); err != nil {
			return err
		}
		// If a new username was provided, also create directories/authorized_keys in the same operation
		if message.NewUsername != "" {
			if err := RecreateUserDirectoriesOnInstances(queries, message.UserID, message.OldUsername, message.NewUsername, awsRootRegion, secretKey); err != nil {
				return err
			}
		}
		return nil
	case "USERNAME_CREATE":
		// Legacy path: keep for compatibility if ever used
		return RecreateUserDirectoriesOnInstances(queries, message.UserID, message.OldUsername, message.NewUsername, awsRootRegion, secretKey)
	default:
		return fmt.Errorf("unknown operation type: %s", message.OperationType)
	}
}
