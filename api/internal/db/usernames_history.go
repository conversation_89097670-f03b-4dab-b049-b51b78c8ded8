package db

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

// InsertDeletedUsernameHistory records a deleted username for a user.
// This is called when a user's custom_username changes; the previous
// username is inserted into history with the deletion timestamp.
func (q *Queries) InsertDeletedUsernameHistory(ctx context.Context, userID pgtype.UUID, deletedUsername string) error {
	_, err := q.db.Exec(ctx,
		`INSERT INTO usernames_history (user_id, username, operation_status) VALUES ($1, $2, NULL)`,
		userID, deletedUsername,
	)
	return err
}

// SetOperationUsername updates the username column for a given operation row
func (q *Queries) SetOperationUsername(ctx context.Context, operationID pgtype.UUID, username string) error {
	_, err := q.db.Exec(ctx,
		`UPDATE usernames_history SET username = $2 WHERE id = $1`,
		operationID, username,
	)
	return err
}
